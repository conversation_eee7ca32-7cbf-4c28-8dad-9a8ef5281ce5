import json
import random
import time
from locust import HttpUser, task, between


class CredexonUser(HttpUser):
    """
    Locust user class for the Credexon B2B API.
    This script provides a comprehensive load test covering various endpoints
    from the provided Swagger/OpenAPI specification.
    It includes authentication, different request types (GET, POST, PUT, DELETE),
    and handles various content types.
    """

    # Set the host from the Swagger file's servers. You might want to change this
    # to your desired testing environment (e.g., 'https://preapi.credexon.com').
    host = "http://api.credexon.com"

    # Define the wait time between consecutive tasks for a user
    wait_time = between(1, 5)  # Users will wait between 1 and 5 seconds between tasks

    # A dictionary to store tokens for authenticated users.
    # In a real-world scenario, you'd manage user credentials and tokens more robustly.
    # For a simple load test, we can simulate multiple users logging in.
    _auth_tokens = {}

    def on_start(self):
        """
        On start up, each user will attempt to log in to get an authentication token.
        This token will be used for subsequent authenticated requests.
        """
        self.client.headers = {}  # Reset headers for each new user instance
        self.login()

    def login(self):
        """
        Performs a user login and stores the bearer token.
        This uses the /admin/v1/vendor/login endpoint.
        You might need to adjust the email and password based on your test environment.
        """
        with self.client.post(
            "/admin/v1/vendor/login",
            json={
                "email": "<EMAIL>",  # Replace with a valid test user email
                "password": "LvYFJfVw*q",  # Replace with a valid test user password
            },
            name="/admin/v1/vendor/login",  # Group requests for better stats
            catch_response=True,  # Crucial for manually marking success/failure
        ) as response:
            if response.status_code == 200:
                try:
                    token = response.json().get("data").get("user").get("token")
                    if token:
                        self.client.headers.update({"Authorization": f"Bearer {token}"})
                        self.auth_token = token
                        self._auth_tokens[self.environment.runner.user_count] = (
                            token  # Store token for this user instance
                        )
                        response.success()
                        print(
                            f"User {self.environment.runner.user_count} logged in successfully."
                        )
                    else:
                        response.failure(
                            f"Login failed for user {self.environment.runner.user_count}: Token not found in response."
                        )
                except json.JSONDecodeError:
                    response.failure(
                        f"Login failed for user {self.environment.runner.user_count}: Invalid JSON response."
                    )
            else:
                response.failure(
                    f"Login failed for user {self.environment.runner.user_count} with status code {response.status_code}: {response.text}"
                )

    # --- Admin Endpoints ---

    @task(5)
    def get_admin_banner_list(self):
        """
        GET /admin/v1/banner/list
        Get list of banners (requires authentication).
        """
        self.client.get("/admin/v1/banner/list", name="/admin/v1/banner/list")

    @task(3)
    def create_admin_banner(self):
        """
        POST /admin/v1/banner/save
        Create a new banner (requires authentication).
        """
        banner_data = {
            "type": "homepage",
            "image": f"https://example.com/banner_{int(time.time())}.jpg",
            "sequence": random.randint(1, 10),
            "status": 1,
            "banner_link": "https://example.com/new-banner-page",
        }
        self.client.post(
            "/admin/v1/banner/save", json=banner_data, name="/admin/v1/banner/save"
        )

    @task(2)
    def update_admin_cms(self):
        """
        PUT /admin/v1/cms/update
        Update a CMS entry (requires authentication).
        Requires a valid cms_id. Using a placeholder for now.
        """
        cms_id = "63ce4b35cceb89438dc41a81"  # Replace with a dynamic CMS ID if possible
        update_data = {
            "title": f"Updated Contact Us {int(time.time())}",
            "slug": "contact-us-updated",
            "content": "<p>Updated content for contact us page.</p>",
            "cms_id": cms_id,
        }
        self.client.put(
            "/admin/v1/cms/update", json=update_data, name="/admin/v1/cms/update"
        )

    @task(4)
    def get_admin_cms_list(self):
        """
        GET /admin/v1/cms/list
        Get all CMS entries.
        """
        self.client.get("/admin/v1/cms/list", name="/admin/v1/cms/list")

    @task(1)
    def view_admin_cms(self):
        """
        POST /admin/v1/cms/view
        View a specific CMS entry.
        Requires cms_id. Using a placeholder for now.
        """
        cms_id = "63ce4b35cceb89438dc41a81"  # Example ID, replace with a valid existing CMS ID
        self.client.post(
            "/admin/v1/cms/view", json={"cms_id": cms_id}, name="/admin/v1/cms/view"
        )

    @task(3)
    def create_admin_contest(self):
        """
        POST /admin/v1/contest/create
        Create a new contest (requires authentication).
        """
        contest_data = {
            "title": f"Locust Test Contest {int(time.time())}",
            "subtitle": "Win big with Locust!",
            "dis_val": random.randint(5, 50),
        }
        self.client.post(
            "/admin/v1/contest/create",
            json=contest_data,
            name="/admin/v1/contest/create",
        )

    @task(5)
    def get_admin_match_list(self):
        """
        GET /admin/v1/match/list
        Get match list (requires authentication).
        """
        self.client.get("/admin/v1/match/list", name="/admin/v1/match/list")

    @task(2)
    def send_admin_notification(self):
        """
        POST /admin/v1/notification/send_message
        Send a notification message (requires authentication).
        Using placeholder user IDs.
        """
        notification_data = {
            "userids": [
                "677d5dc722250e77102f4aed"
            ],  # Example user IDs, replace with valid ones if needed
            "isexpire": 1,
            "type": "info",
            "body": f"Load test message from Locust at {time.ctime()}",
            "title": "Locust Test Notification",
        }
        self.client.post(
            "/admin/v1/notification/send_message",
            json=notification_data,
            name="/admin/v1/notification/send_message",
        )

    @task(2)
    def admin_get_all_contact_us_requests(self):
        """
        GET /admin/v1/contactus/get_all_contact_us_requests
        Retrieve all contact us requests (requires authentication).
        """
        self.client.get(
            "/admin/v1/contactus/get_all_contact_us_requests",
            name="/admin/v1/contactus/get_all_contact_us_requests",
        )

    @task(3)
    def admin_get_dashboard_list(self):
        """
        GET /admin/v1/dashboard/list
        Get dashboard list (requires authentication).
        """
        self.client.get("/admin/v1/dashboard/list", name="/admin/v1/dashboard/list")

    @task(2)
    def admin_activate_deactivate_football_match(self):
        """
        POST /admin/v1/football/active-inactive
        Activate or deactivate a football match (requires authentication).
        """
        match_id = 101  # Placeholder match ID
        is_active = random.choice([0, 1])  # Randomly activate or deactivate
        self.client.post(
            "/admin/v1/football/active-inactive",
            json={"match_id": match_id, "is_active": is_active},
            name="/admin/v1/football/active-inactive",
        )

    @task(2)
    def admin_update_player_accumulator(self):
        """
        POST /admin/v1/playeraccumulator/update_player
        Update player accumulator details.
        """
        player_id = 1  # Placeholder
        accumulator_score = round(random.uniform(10.0, 100.0), 2)
        self.client.post(
            "/admin/v1/playeraccumulator/update_player",
            json={"player_id": player_id, "accumulator_score": accumulator_score},
            name="/admin/v1/playeraccumulator/update_player",
        )

    @task(1)
    def admin_create_category(self):
        """
        POST /admin/v1/category/create
        Create or update a category (requires authentication).
        """
        category_name = f"Test Category {int(time.time())}"
        self.client.post(
            "/admin/v1/category/create",
            json={"name": category_name, "description": "A category created by Locust"},
            name="/admin/v1/category/create",
        )

    @task(1)
    def admin_get_users_list(self):
        """
        GET /admin/v1/users/list
        Get list of users (requires authentication).
        """
        self.client.get("/admin/v1/users/list", name="/admin/v1/users/list")

    @task(1)
    def admin_change_user_password(self):
        """
        POST /admin/v1/users/change_password
        Change user password (requires authentication).
        Note: This would typically require the current user's old and new password.
        For load testing, you might use pre-registered users with known passwords.
        """
        self.client.post(
            "/admin/v1/users/change_password",
            json={"oldPassword": "Password123!", "newPassword": "NewPassword123!"},
            name="/admin/v1/users/change_password",
        )

    @task(1)
    def admin_verify_bank(self):
        """
        POST /admin/v1/users/bank_verified
        Verify or reject a user's bank information.
        """
        user_id = 101  # Placeholder user ID
        is_verified = random.choice([0, 1])
        data = {"userid": user_id, "isverified": is_verified}
        if is_verified == 0:
            data["reject_reason"] = "Test rejection reason"
        self.client.post(
            "/admin/v1/users/bank_verified",
            json=data,
            name="/admin/v1/users/bank_verified",
        )

    @task(1)
    def admin_process_team_accumulator(self):
        """
        POST /socket/v1/accmulator/teamaccmulator
        Process team accumulator data (requires authentication).
        Request body is an empty object example in swagger.
        """
        self.client.post(
            "/socket/v1/accmulator/teamaccmulator",
            json={},
            name="/socket/v1/accmulator/teamaccmulator",
        )

    # --- App Endpoints ---

    @task(4)
    def get_app_banner_list(self):
        """
        POST /app/v1/banners/list
        Get list of banners for the app (form-urlencoded).
        """
        self.client.post(
            "/app/v1/banners/list",
            data={"type": "homepage", "device": "mobile"},
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/app/v1/banners/list",
        )

    @task(3)
    def get_app_cms_list(self):
        """
        POST /app/v1/cms/list
        Get list of CMS items for the app.
        """
        self.client.post("/app/v1/cms/list", name="/app/v1/cms/list")

    @task(2)
    def app_user_register(self):
        """
        POST /app/v1/users/register
        Register a new user (form-urlencoded).
        Use unique data for each call in a real test.
        """
        unique_phone = f"987654{random.randint(1000, 9999)}"
        unique_email = f"user_{int(time.time())}{random.randint(100,999)}@example.com"
        register_data = {
            "email": unique_email,
            "phone": unique_phone,
            "password": "SecurePassword123!",
            "country_code": "+91",
            "usertype": 1,
            "logintype": "manual",
        }
        self.client.post(
            "/app/v1/users/register",
            data=register_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/app/v1/users/register",
        )

    @task(1)
    def app_user_login(self):
        """
        POST /app/v1/users/login
        User login for frontend (form-urlencoded).
        """
        # This login is separate from the admin login.
        # For a full test, you'd manage these credentials dynamically.
        login_data = {
            "country_code": "+91",
            "phone": "7021216784",
            "usertype": 2,
            "logintype": "N",
            "apikey": "crdxn",
            "lat": 19.088574114256957,
            "long": 73.00165696197719,
            "state_name": "Maharashtra",
        }
        self.client.post(
            "/app/v1/users/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/app/v1/users/login_frontend",
        )

    @task(3)
    def get_app_profile(self):
        """
        POST /app/v1/users/get_profile
        Get user profile by user ID (form-urlencoded, requires auth).
        Using a placeholder user ID.
        """
        user_id = "67db31033f3be213ac365ec7"  # Replace with a valid user ID, potentially obtained from a login
        self.client.post(
            "/app/v1/users/get_profile",
            data={"userid": user_id},
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/app/v1/users/get_profile",
        )

    @task(2)
    def get_app_match_publish_list(self):
        """
        POST /app/v1/match/publish_match_list
        Get list of active cricket matches based on status.
        """
        self.client.post(
            "/app/v1/match/publish_match_list",
            json={"rstatus": 1},  # Example status: 1 for active
            name="/app/v1/match/publish_match_list",
        )

    @task(1)
    def app_join_pool(self):
        """
        POST /app/v1/pool/app/v1/pool/join_pool
        Join a pool contest (requires authentication).
        Uses placeholder IDs.
        """
        join_data = {
            "poolid": "some_pool_id_123",  # Replace with actual pool ID
            "match_id": 101,  # Replace with actual match ID
            "gametype": "cricket",
            "uteamid": "some_user_team_id_xyz",  # Replace with actual user team ID
        }
        self.client.post(
            "/app/v1/pool/app/v1/pool/join_pool",
            json=join_data,
            name="/app/v1/pool/join_pool",
        )

    @task(1)
    def app_create_private_contest_match(self):
        """
        POST /app/v1/pool/privatecontest/save
        Save a private contest for a match (requires authentication).
        Requires complex data, using a simplified example.
        """
        private_contest_data = {
            "match_id": 123,
            "privatename": f"My Private Contest {int(time.time())}",
            "maxteams": random.randint(2, 10),
            "joinfee": random.randint(10, 100),
            "winners": 1,
            "s": 0,  # Placeholder
            "m": 0,  # Placeholder
            "totalwinamt": random.randint(100, 1000),
            "gtype": "ckt",
            "poolpb": [{"pmin": 1, "pmax": 1, "pamount": random.randint(50, 500)}],
        }
        self.client.post(
            "/app/v1/pool/privatecontest/save",
            json=private_contest_data,
            name="/app/v1/pool/privatecontest/save",
        )

    @task(1)
    def admin_delete_subadmin(self):
        """
        DELETE /admin/v1/subadmin/delete
        Delete a subadmin user (requires authentication).
        Requires specific subadmin_id. Use with caution in a test environment.
        """
        # Note: In a real test, you'd likely create a subadmin first and then delete it
        # using the ID returned from the creation. This is a placeholder.
        subadmin_id_to_delete = "some_subadmin_id"  # Replace with actual ID
        self.client.delete(
            "/admin/v1/subadmin/delete",
            data={"id": subadmin_id_to_delete},
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/admin/v1/subadmin/delete",
        )

    @task(1)
    def admin_delete_banner(self):
        """
        POST /admin/v1/banner/edit with status 0 or a separate delete endpoint
        The swagger does not have a direct delete for banners, but often 'edit' with status 0 or similar is used,
        or a dedicated delete endpoint exists that wasn't in the provided snippet.
        Assuming a delete by ID if one were present, or a logical delete via update.
        For now, let's skip actual deletion and simulate a "soft delete" if possible, or mark as a skipped task.
        """
        # As there is no direct DELETE /admin/v1/banner/delete, we simulate a possible 'deactivation'
        # if the 'edit' endpoint supports setting a status to 'inactive' (e.g., status: 0).
        # Otherwise, this task would represent a missing but desired endpoint.
        # banner_id_to_deactivate = "64b3e0a5e45d10001c0def12" # Placeholder
        # self.client.post(
        #     "/admin/v1/banner/edit",
        #     json={
        #         "banner_id": banner_id_to_deactivate,
        #         "status": 0 # Assuming 0 means inactive/deleted
        #     },
        #     name="/admin/v1/banner/deactivate"
        # )
        pass  # Skipping actual delete for now due to lack of a clear DELETE endpoint in swagger.
        print(
            "Skipping admin_delete_banner as no direct DELETE endpoint is clearly defined."
        )

    @task(1)
    def admin_process_all_accumulator(self):
        """
        POST /socket/v1/accmulator/plyallaccumulator
        Process all accumulator data (requires authentication).
        Request body is an empty object example in swagger.
        """
        self.client.post(
            "/socket/v1/accmulator/plyallaccumulator",
            json={},
            name="/socket/v1/accmulator/plyallaccumulator",
        )

    @task(1)
    def admin_get_cache_storage(self):
        """
        POST /socket/v1/cache/cachestorageget
        Get data from socket cache storage (requires authentication).
        """
        self.client.post(
            "/socket/v1/cache/cachestorageget",
            json={"key": "match_list"},  # Example key
            name="/socket/v1/cache/cachestorageget",
        )

    @task(2)
    def app_get_cricket_series_list(self):
        """
        POST /app/v1/match/cricket/series
        Get list of cricket series by status (requires authentication).
        """
        self.client.post(
            "/app/v1/match/cricket/series",
            json={"status": "active"},
            name="/app/v1/match/cricket/series",
        )

    @task(2)
    def app_get_football_series_list(self):
        """
        POST /app/v1/match/football/series
        Get list of football series by status (requires authentication).
        """
        self.client.post(
            "/app/v1/match/football/series",
            json={"status": "upcoming"},
            name="/app/v1/match/football/series",
        )

    @task(2)
    def app_get_player_list(self):
        """
        POST /app/v1/match/player_list
        Get player list filtered by type and match ID (requires authentication).
        """
        self.client.post(
            "/app/v1/match/player_list",
            json={"type": "cricket", "match_id": 101},  # Placeholder match_id
            name="/app/v1/match/player_list",
        )

    @task(1)
    def app_add_coins(self):
        """
        POST /app/v1/match/add_coins
        Add coins to user account (requires authentication).
        """
        self.client.post(
            "/app/v1/match/add_coins",
            json={"coins": random.randint(10, 100)},
            name="/app/v1/match/add_coins",
        )

    @task(1)
    def app_get_live_cricket_score(self):
        """
        POST /app/v1/match/live_score
        Get live cricket match score (requires authentication).
        """
        self.client.post(
            "/app/v1/match/live_score", json={}, name="/app/v1/match/live_score"
        )

    @task(1)
    def app_get_match_contest_list(self):
        """
        POST /app/v1/pool/match_contest
        Get contest list for a specific match.
        """
        self.client.post(
            "/app/v1/pool/match_contest",
            json={"match_id": 101},  # Placeholder match_id
            name="/app/v1/pool/match_contest",
        )

    @task(1)
    def app_verify_phone(self):
        """
        POST /app/v1/users/verifyphone
        Verify user phone number (form-urlencoded).
        This typically involves an OTP. For load testing, you might bypass OTP or use a fixed one for testing.
        """
        phone_number = f"998877{random.randint(1000, 9999)}"
        self.client.post(
            "/app/v1/users/verifyphone",
            data={
                "country_code": "+91",
                "phone": phone_number,
                "otp": 123456,  # Placeholder OTP
                "isVerifed": True,
                "name": f"Test User {int(time.time())}",
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="/app/v1/users/verifyphone",
        )

    @task(1)
    def app_send_email_otp(self):
        """
        POST /app/v1/users/send-email-otp
        Send OTP to email (requires authentication).
        """
        self.client.post(
            "/app/v1/users/send-email-otp",
            json={"email": f"email_otp_test_{int(time.time())}@example.com"},
            name="/app/v1/users/send-email-otp",
        )

    @task(1)
    def app_get_invite_count(self):
        """
        POST /app/v1/users/invite_count
        Get count of successful invites (requires authentication).
        """
        self.client.post(
            "/app/v1/users/invite_count", name="/app/v1/users/invite_count"
        )

    @task(1)
    def app_get_wallet_view(self):
        """
        GET /app/v1/users/wallet_view
        View user wallet details (requires authentication).
        """
        self.client.get("/app/v1/users/wallet_view", name="/app/v1/users/wallet_view")

    @task(1)
    def socket_get_match_football_list(self):
        """
        POST /socket/v1/match/publish_match_football_list
        Get active football match list for publishing (requires authentication).
        """
        self.client.post(
            "/socket/v1/match/publish_match_football_list",
            json={"rstatus": 1, "page": 1, "limit": 10},
            name="/socket/v1/match/publish_match_football_list",
        )

    @task(1)
    def socket_get_pool_details(self):
        """
        POST /socket/v1/pool/pooldetails
        Get pool details for a specific match (requires authentication).
        """
        self.client.post(
            "/socket/v1/pool/pooldetails",
            json={"match_id": "12345"},  # Placeholder match_id
            name="/socket/v1/pool/pooldetails",
        )

    @task(1)
    def admin_banner_edit(self):
        """
        POST /admin/v1/banner/edit
        Summary: Edit an existing banner
        Requires Auth: True
        """
        request_data = {
            "banner_id": "64b3e0a5e45d10001c0def12",  # Placeholder - replace with actual ID
            "type": "homepage",
            "image": f"https://example.com/banner-updated_{int(time.time())}.jpg",
            "sequence": random.randint(1, 10),
            "device": "mobile",
            "status": random.choice([0, 1]),
            "banner_link": "https://example.com/updated-link",
        }
        self.client.post(
            "/admin/v1/banner/edit",
            json=request_data,
            name="POST /admin/v1/banner/edit",
        )

    @task(1)
    def admin_category_list(self):
        """
        GET /admin/v1/category/list
        Summary: Get list of categories
        Requires Auth: False
        """
        self.client.get("/admin/v1/category/list", name="GET /admin/v1/category/list")

    @task(1)
    def admin_cms_create(self):
        """
        POST /admin/v1/cms/create
        Summary: Create a new CMS entry
        Requires Auth: False
        """
        request_data = {
            "title": f"New CMS Page {int(time.time())}",
            "slug": f"new-cms-page-{int(time.time())}",
            "content": "<p>This is the content for the new CMS page.</p>",
        }
        self.client.post(
            "/admin/v1/cms/create", json=request_data, name="POST /admin/v1/cms/create"
        )

    @task(1)
    def admin_contest_view(self):
        """
        POST /admin/v1/contest/view
        Summary: View a specific contest
        Requires Auth: True
        """
        request_data = {
            "contest_id": "648f8c9e8f4b9a2b12345678"  # Placeholder - replace with actual ID
        }
        self.client.post(
            "/admin/v1/contest/view",
            json=request_data,
            name="POST /admin/v1/contest/view",
        )

    @task(1)
    def admin_contest_update(self):
        """
        POST /admin/v1/contest/update
        Summary: Update an existing contest
        Requires Auth: True
        """
        request_data = {
            "contest_id": "648f8c9e8f4b9a2b12345678",  # Placeholder - replace with actual ID
            "title": f"Updated Contest Title {int(time.time())}",
            "subtitle": "Updated Contest Subtitle",
            "contestlogo": "https://example.com/logo.png",
            "dis_val": random.randint(10, 60),
        }
        self.client.post(
            "/admin/v1/contest/update",
            json=request_data,
            name="POST /admin/v1/contest/update",
        )

    @task(1)
    def admin_contest_active_inactive(self):
        """
        POST /admin/v1/contest/active-inactive
        Summary: Toggle contest active status
        Requires Auth: True
        """
        request_data = {
            "contest_id": "648f8c9e8f4b9a2b12345678",  # Placeholder - replace with actual ID
            "status": random.choice([0, 1]),
        }
        self.client.post(
            "/admin/v1/contest/active-inactive",
            json=request_data,
            name="POST /admin/v1/contest/active-inactive",
        )

    @task(1)
    def admin_contest_change_contest_order(self):
        """
        POST /admin/v1/contest/change-contest-order
        Summary: Change contest display order
        Requires Auth: True
        """
        # Request body is not clearly defined in snippet, using empty object as per schema reference
        request_data = (
            {}
        )  # Assuming an empty object or structure based on $ref not provided
        self.client.post(
            "/admin/v1/contest/change-contest-order",
            json=request_data,
            name="POST /admin/v1/contest/change-contest-order",
        )

    @task(1)
    def admin_contest_list(self):
        """
        GET /admin/v1/contest/list
        Summary: List all contests
        Requires Auth: True
        """
        self.client.get("/admin/v1/contest/list", name="GET /admin/v1/contest/list")

    @task(1)
    def admin_dashboard_dashboard_vender_details(self):
        """
        POST /admin/v1/dashboard/dashboard_vender_details
        Summary: Get vendor details for dashboard
        Requires Auth: True
        """
        request_data = {
            "id": "64a1e2c45f2e3a001234abcd"  # Placeholder - replace with actual ID
        }
        self.client.post(
            "/admin/v1/dashboard/dashboard_vender_details",
            json=request_data,
            name="POST /admin/v1/dashboard/dashboard_vender_details",
        )

    @task(1)
    def admin_football_list(self):
        """
        GET /admin/v1/football/list
        Summary: Get list of football matches
        Requires Auth: True
        """
        self.client.get("/admin/v1/football/list", name="GET /admin/v1/football/list")

    @task(1)
    def admin_football_football_activity_list(self):
        """
        GET /admin/v1/football/football_activity_list
        Summary: Get list of football match activities
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/football/football_activity_list",
            name="GET /admin/v1/football/football_activity_list",
        )

    @task(1)
    def admin_football_cancel_match(self):
        """
        POST /admin/v1/football/cancel-match
        Summary: Cancel a football match
        Requires Auth: True
        """
        request_data = {"match_id": 101, "is_active": 0}  # Placeholder match ID
        self.client.post(
            "/admin/v1/football/cancel-match",
            json=request_data,
            name="POST /admin/v1/football/cancel-match",
        )

    @task(1)
    def admin_football_active_list(self):
        """
        GET /admin/v1/football/active_list
        Summary: Get list of active football matches
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/football/active_list", name="GET /admin/v1/football/active_list"
        )

    @task(1)
    def admin_football_publish(self):
        """
        POST /admin/v1/football/publish
        Summary: Publish or unpublish a football match
        Requires Auth: True
        """
        request_data = {
            "match_id": 101,  # Placeholder match ID
            "is_publish": random.choice([0, 1]),
        }
        self.client.post(
            "/admin/v1/football/publish",
            json=request_data,
            name="POST /admin/v1/football/publish",
        )

    @task(1)
    def admin_accumulator_match_list(self):
        """
        POST /admin/v1/accumulator/match_list
        Summary: Get player list by match
        Requires Auth: False
        """
        request_data = {"type": "T20", "match_id": 101}
        self.client.post(
            "/admin/v1/accumulator/match_list",
            json=request_data,
            name="POST /admin/v1/accumulator/match_list",
        )

    @task(1)
    def admin_match_cricket_activity_list(self):
        """
        GET /admin/v1/match/cricket_activity_list
        Summary: Get cricket activity list
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/cricket_activity_list",
            name="GET /admin/v1/match/cricket_activity_list",
        )

    @task(1)
    def admin_match_listbystatus(self):
        """
        GET /admin/v1/match/listByStatus
        Summary: Get match list filtered by status
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/listByStatus", name="GET /admin/v1/match/listByStatus"
        )

    @task(1)
    def admin_match_footballlistbystatus(self):
        """
        GET /admin/v1/match/footballlistByStatus
        Summary: Get football match list filtered by status
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/footballlistByStatus",
            name="GET /admin/v1/match/footballlistByStatus",
        )

    @task(1)
    def admin_match_series_list(self):
        """
        GET /admin/v1/match/series/list
        Summary: Get list of series matches
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/series/list", name="GET /admin/v1/match/series/list"
        )

    @task(1)
    def admin_match_series_active_list(self):
        """
        GET /admin/v1/match/series/active_list
        Summary: Get active series list
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/series/active_list",
            name="GET /admin/v1/match/series/active_list",
        )

    @task(1)
    def admin_match_series_active_inactive(self):
        """
        POST /admin/v1/match/series/active-inactive
        Summary: Activate or deactivate a series (Cricket)
        Requires Auth: True
        """
        request_data = {}  # Assuming empty or specific fields not detailed in snippet
        self.client.post(
            "/admin/v1/match/series/active-inactive",
            json=request_data,
            name="POST /admin/v1/match/series/active-inactive",
        )

    @task(1)
    def admin_match_series_football_active_inactive(self):
        """
        POST /admin/v1/match/series/football/active_inactive
        Summary: Activate or deactivate a football series
        Requires Auth: True
        """
        request_data = {}  # Assuming empty or specific fields not detailed in snippet
        self.client.post(
            "/admin/v1/match/series/football/active_inactive",
            json=request_data,
            name="POST /admin/v1/match/series/football/active_inactive",
        )

    @task(1)
    def admin_match_active_inactive(self):
        """
        POST /admin/v1/match/active-inactive
        Summary: Activate or deactivate a match
        Requires Auth: True
        """
        request_data = {"match_id": 101, "is_active": random.choice([0, 1])}
        self.client.post(
            "/admin/v1/match/active-inactive",
            json=request_data,
            name="POST /admin/v1/match/active-inactive",
        )

    @task(1)
    def admin_match_cancel_match(self):
        """
        POST /admin/v1/match/cancel-match
        Summary: Cancel a match
        Requires Auth: True
        """
        request_data = {"match_id": 101, "is_active": 0}
        self.client.post(
            "/admin/v1/match/cancel-match",
            json=request_data,
            name="POST /admin/v1/match/cancel-match",
        )

    @task(1)
    def admin_match_active_list(self):
        """
        GET /admin/v1/match/active_list
        Summary: Get active matches list
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/active_list", name="GET /admin/v1/match/active_list"
        )

    @task(1)
    def admin_match_publish(self):
        """
        POST /admin/v1/match/publish
        Summary: Publish or unpublish a match
        Requires Auth: True
        """
        request_data = {"match_id": 101, "is_publish": random.choice([0, 1])}
        self.client.post(
            "/admin/v1/match/publish",
            json=request_data,
            name="POST /admin/v1/match/publish",
        )

    @task(1)
    def admin_match_fantasy_list(self):
        """
        GET /admin/v1/match/fantasy_list
        Summary: Get fantasy match list
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/match/fantasy_list", name="GET /admin/v1/match/fantasy_list"
        )

    @task(1)
    def admin_match_fantasygame_list(self):
        """
        POST /admin/v1/match/fantasygame_list
        Summary: Get fantasy game list by game ID
        Requires Auth: True
        """
        request_data = {"game_id": 123}
        self.client.post(
            "/admin/v1/match/fantasygame_list",
            json=request_data,
            name="POST /admin/v1/match/fantasygame_list",
        )

    @task(1)
    def admin_match_fantasygame_type_list(self):
        """
        POST /admin/v1/match/fantasygame_type_list
        Summary: Get fantasy game type list by game ID and type
        Requires Auth: True
        """
        request_data = {"game_id": 123, "type": "premium"}
        self.client.post(
            "/admin/v1/match/fantasygame_type_list",
            json=request_data,
            name="POST /admin/v1/match/fantasygame_type_list",
        )

    @task(1)
    def admin_match_update_fantasygame_points(self):
        """
        PUT /admin/v1/match/update_fantasygame_points
        Summary: Update fantasy game points
        Requires Auth: True
        """
        request_data = {
            "game_id": 123,
            "type": "premium",
            "points": {"player1": 10, "player2": 15},
        }
        self.client.put(
            "/admin/v1/match/update_fantasygame_points",
            json=request_data,
            name="PUT /admin/v1/match/update_fantasygame_points",
        )

    @task(1)
    def admin_match_current_match_list(self):
        """
        POST /admin/v1/match/current_match_list
        Summary: Get current match list
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/match/current_match_list",
            json=request_data,
            name="POST /admin/v1/match/current_match_list",
        )

    @task(1)
    def admin_notification_list(self):
        """
        POST /admin/v1/notification/list
        Summary: Get list of notifications
        Requires Auth: False
        """
        request_data = {}
        self.client.post(
            "/admin/v1/notification/list",
            json=request_data,
            name="POST /admin/v1/notification/list",
        )

    @task(1)
    def admin_playeraccumulator_series_list(self):
        """
        GET /admin/v1/playeraccumulator/series_list
        Summary: Get list of series for player accumulator
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/playeraccumulator/series_list",
            name="GET /admin/v1/playeraccumulator/series_list",
        )

    @task(1)
    def admin_playeraccumulator_match_list(self):
        """
        POST /admin/v1/playeraccumulator/match_list
        Summary: Get list of matches based on series
        Requires Auth: True
        """
        request_data = {"series_id": 101}
        self.client.post(
            "/admin/v1/playeraccumulator/match_list",
            json=request_data,
            name="POST /admin/v1/playeraccumulator/match_list",
        )

    @task(1)
    def admin_playeraccumulator_player_list(self):
        """
        POST /admin/v1/playeraccumulator/player_list
        Summary: Get list of players for selected match
        Requires Auth: False
        """
        request_data = {"match_id": 501}
        self.client.post(
            "/admin/v1/playeraccumulator/player_list",
            json=request_data,
            name="POST /admin/v1/playeraccumulator/player_list",
        )

    @task(1)
    def admin_plymetadata_ply_add(self):
        """
        POST /admin/v1/plymetadata/ply_add
        Summary: Add or update player metadata
        Requires Auth: False
        """
        request_data = {
            "type": "cricket",
            "pid": 1,
            "logo_url": f"https://example.com/player_logo_{int(time.time())}.png",
            "jersy_no": random.randint(1, 99),
        }
        self.client.post(
            "/admin/v1/plymetadata/ply_add",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/plymetadata/ply_add",
        )

    @task(1)
    def admin_plymetadata_cricket_status(self):
        """
        POST /admin/v1/plymetadata/cricket/status
        Summary: Update cricket player status
        Requires Auth: False
        """
        request_data = {"pid": 1, "status": random.choice([0, 1])}
        self.client.post(
            "/admin/v1/plymetadata/cricket/status",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/plymetadata/cricket/status",
        )

    @task(1)
    def admin_plymetadata_football_status(self):
        """
        POST /admin/v1/plymetadata/football/status
        Summary: Update football player status
        Requires Auth: False
        """
        request_data = {"pid": 1, "status": random.choice([0, 1])}
        self.client.post(
            "/admin/v1/plymetadata/football/status",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/plymetadata/football/status",
        )

    @task(1)
    def admin_plymetadata_player_list(self):
        """
        POST /admin/v1/plymetadata/player_list
        Summary: Get list of players
        Requires Auth: True
        """
        request_data = {}  # Assuming no specific parameters required as per snippet
        self.client.post(
            "/admin/v1/plymetadata/player_list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/plymetadata/player_list",
        )

    @task(1)
    def admin_plymetadata_sereis_player_list(self):
        """
        POST /admin/v1/plymetadata/sereis_player_list
        Summary: Get list of players for a series
        Requires Auth: True
        """
        request_data = {"type": "cricket", "league_id": 1, "contest_id": "CT123"}
        self.client.post(
            "/admin/v1/plymetadata/sereis_player_list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/plymetadata/sereis_player_list",
        )

    @task(1)
    def admin_poolmaster_create(self):
        """
        POST /admin/v1/poolmaster/create
        Summary: Create a new pool
        Requires Auth: True
        """
        request_data = (
            {}
        )  # Example body not explicitly given in swagger, using empty object
        self.client.post(
            "/admin/v1/poolmaster/create",
            json=request_data,
            name="POST /admin/v1/poolmaster/create",
        )

    @task(1)
    def admin_poolmaster_pool_calculation(self):
        """
        POST /admin/v1/poolmaster/pool_calculation
        Summary: Perform pool calculation
        Requires Auth: True
        """
        request_data = (
            {}
        )  # Example body not explicitly given in swagger, using empty object
        self.client.post(
            "/admin/v1/poolmaster/pool_calculation",
            json=request_data,
            name="POST /admin/v1/poolmaster/pool_calculation",
        )

    @task(1)
    def admin_poolmaster_series_datetime_pool(self):
        """
        POST /admin/v1/poolmaster/series_datetime_pool
        Summary: Save series pool date/time information
        Requires Auth: True
        """
        request_data = {
            "contest_id": "CT2025",
            "date_start": "2025-06-01",
            "date_end": "2025-06-10",
            "matchid_start": 101,
            "matchid_end": 110,
            "gtype": "cricket",
            "league_id": 5,
            "session_id": 2,
        }
        self.client.post(
            "/admin/v1/poolmaster/series_datetime_pool",
            json=request_data,
            name="POST /admin/v1/poolmaster/series_datetime_pool",
        )

    @task(1)
    def admin_poolmaster_pool_range(self):
        """
        POST /admin/v1/poolmaster/pool_range
        Summary: Create prize range for a pool
        Requires Auth: True
        """
        request_data = {"poolmaster_id": "PM001", "pmin": 1, "pmax": 10, "pamount": 500}
        self.client.post(
            "/admin/v1/poolmaster/pool_range",
            json=request_data,
            name="POST /admin/v1/poolmaster/pool_range",
        )

    @task(1)
    def admin_poolmaster_cricket_active_pool_list(self):
        """
        POST /admin/v1/poolmaster/cricket/active_pool_list
        Summary: Get active cricket pool contest list for a match
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/cricket/active_pool_list",
            json=request_data,
            name="POST /admin/v1/poolmaster/cricket/active_pool_list",
        )

    @task(1)
    def admin_poolmaster_cricket_active_contest_pool_status_list(self):
        """
        POST /admin/v1/poolmaster/cricket/active_contest_pool_status_list
        Summary: Get active cricket contest pool status list for a match
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/cricket/active_contest_pool_status_list",
            json=request_data,
            name="POST /admin/v1/poolmaster/cricket/active_contest_pool_status_list",
        )

    @task(1)
    def admin_poolmaster_cricket_active_all_contest_pool_status_list(self):
        """
        POST /admin/v1/poolmaster/cricket/active_all_contest_pool_status_list
        Summary: Get all active cricket contest pool status list
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/cricket/active_all_contest_pool_status_list",
            json=request_data,
            name="POST /admin/v1/poolmaster/cricket/active_all_contest_pool_status_list",
        )

    @task(1)
    def admin_poolmaster_all_active_pool_list(self):
        """
        POST /admin/v1/poolmaster/all_active_pool_list
        Summary: Get list of all active contest pools
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/all_active_pool_list",
            json=request_data,
            name="POST /admin/v1/poolmaster/all_active_pool_list",
        )

    @task(1)
    def admin_poolmaster_football_active_pool_list(self):
        """
        POST /admin/v1/poolmaster/football/active_pool_list
        Summary: Get active football pool contest list
        Requires Auth: True
        """
        request_data = {
            "match_id": 202,
            "type": "standard",
            "countrytype": "international",
            "gtype": "football",
            "contest_id": "FT123",
        }
        self.client.post(
            "/admin/v1/poolmaster/football/active_pool_list",
            json=request_data,
            name="POST /admin/v1/poolmaster/football/active_pool_list",
        )

    @task(1)
    def admin_poolmaster_edit_pool_range(self):
        """
        POST /admin/v1/poolmaster/edit_pool_range
        Summary: Edit prize range for a pool
        Requires Auth: True
        """
        request_data = {
            "poolprizebreak_id": "PPB001",
            "pmin": 1,
            "pmax": 5,
            "pamount": 1000,
        }
        self.client.post(
            "/admin/v1/poolmaster/edit_pool_range",
            json=request_data,
            name="POST /admin/v1/poolmaster/edit_pool_range",
        )

    @task(1)
    def admin_poolmaster_active_inactive_pool(self):
        """
        POST /admin/v1/poolmaster/active_inactive_pool
        Summary: Toggle active/inactive status of a pool
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/active_inactive_pool",
            json=request_data,
            name="POST /admin/v1/poolmaster/active_inactive_pool",
        )

    @task(1)
    def admin_poolmaster_active_inactive_contest_pool_match(self):
        """
        POST /admin/v1/poolmaster/active_inactive_contest_pool_match
        Summary: Toggle active/inactive status of contest pool for a match
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/admin/v1/poolmaster/active_inactive_contest_pool_match",
            json=request_data,
            name="POST /admin/v1/poolmaster/active_inactive_contest_pool_match",
        )

    @task(1)
    def admin_poolmaster_delete(self):
        """
        DELETE /admin/v1/poolmaster/delete
        Summary: Delete a pool prize break entry
        Requires Auth: True
        """
        request_data = {"poolprizebreak_id": "PPB001"}
        self.client.delete(
            "/admin/v1/poolmaster/delete",
            json=request_data,
            name="DELETE /admin/v1/poolmaster/delete",
        )

    @task(1)
    def admin_poolmaster_list(self):
        """
        POST /admin/v1/poolmaster/list
        Summary: Get list of pool masters based on contest and game type
        Requires Auth: True
        """
        request_data = {"contest_id": "C123", "gtype": "cricket", "type": "standard"}
        self.client.post(
            "/admin/v1/poolmaster/list",
            json=request_data,
            name="POST /admin/v1/poolmaster/list",
        )

    @task(1)
    def admin_poolmaster_view(self):
        """
        POST /admin/v1/poolmaster/view
        Summary: View detailed pool list for a contest
        Requires Auth: True
        """
        request_data = {"contest_id": "C123"}
        self.client.post(
            "/admin/v1/poolmaster/view",
            json=request_data,
            name="POST /admin/v1/poolmaster/view",
        )

    @task(1)
    def admin_poolmaster_ischecked(self):
        """
        POST /admin/v1/poolmaster/ischecked
        Summary: Mark a pool as checked or unchecked
        Requires Auth: True
        """
        request_data = {"pool_id": "POOL123", "isChecked": random.choice([0, 1])}
        self.client.post(
            "/admin/v1/poolmaster/ischecked",
            json=request_data,
            name="POST /admin/v1/poolmaster/ischecked",
        )

    @task(1)
    def admin_report_get_match_list(self):
        """
        POST /admin/v1/report/get_match_list
        Summary: Get list of matches for reporting
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/report/get_match_list",
            name="POST /admin/v1/report/get_match_list",
        )

    @task(1)
    def admin_report_get_report_data(self):
        """
        POST /admin/v1/report/get_report_data
        Summary: Get report data
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/report/get_report_data",
            name="POST /admin/v1/report/get_report_data",
        )

    @task(1)
    def admin_report_get_report_download(self):
        """
        POST /admin/v1/report/get_report_download
        Summary: Download report data
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/report/get_report_download",
            name="POST /admin/v1/report/get_report_download",
        )

    @task(1)
    def admin_report_get_downloads(self):
        """
        GET /admin/v1/report/get-downloads
        Summary: Get list of downloadable files
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/report/get-downloads", name="GET /admin/v1/report/get-downloads"
        )

    @task(1)
    def admin_report_download_file_fileid(self):
        """
        GET /admin/v1/report/download-file/{fileId}
        Summary: Download a specific file by ID
        Requires Auth: True
        """
        file_id = "test_file_id"  # Placeholder - replace with actual ID
        self.client.get(
            f"/admin/v1/report/download-file/{file_id}",
            name="GET /admin/v1/report/download-file/{fileId}",
        )

    @task(1)
    def admin_report_files(self):
        """
        DELETE /admin/v1/report/files
        Summary: Delete all report files
        Requires Auth: True
        """
        self.client.delete(
            "/admin/v1/report/files", name="DELETE /admin/v1/report/files"
        )

    @task(1)
    def admin_report_get_download_affiliates(self):
        """
        GET /admin/v1/report/get_download_affiliates
        Summary: Get affiliate download tracking data
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/report/get_download_affiliates",
            name="GET /admin/v1/report/get_download_affiliates",
        )

    @task(1)
    def admin_report_mongo_export_csv(self):
        """
        GET /admin/v1/report/mongo_export_csv
        Summary: Export MongoDB data to CSV
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/report/mongo_export_csv",
            name="GET /admin/v1/report/mongo_export_csv",
        )

    @task(1)
    def admin_report_update_user(self):
        """
        GET /admin/v1/report/update_user
        Summary: Update user information
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/report/update_user", name="GET /admin/v1/report/update_user"
        )

    @task(1)
    def admin_series_ckt_team(self):
        """
        POST /admin/v1/series/ckt_team
        Summary: Find cricket team by name
        Requires Auth: False
        """
        request_data = {"name": "India"}
        self.client.post(
            "/admin/v1/series/ckt_team",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/ckt_team",
        )

    @task(1)
    def admin_series_edit_ckt(self):
        """
        POST /admin/v1/series/edit_ckt
        Summary: Edit cricket team details
        Requires Auth: False
        """
        request_data = {
            "team_id": "test_team_id_1",  # Placeholder
            "logo_url": f"https://example.com/ckt_logo_{int(time.time())}.png",
            "short_name": "IND",
        }
        self.client.post(
            "/admin/v1/series/edit_ckt",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/edit_ckt",
        )

    @task(1)
    def admin_series_fb_team_list_old(self):
        """
        GET /admin/v1/series/fb_team_list_old
        Summary: Get list of old football teams
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/series/fb_team_list_old",
            name="GET /admin/v1/series/fb_team_list_old",
        )

    @task(1)
    def admin_series_fb_team(self):
        """
        POST /admin/v1/series/fb_team
        Summary: Find football team by name
        Requires Auth: False
        """
        request_data = {"name": "Brazil"}
        self.client.post(
            "/admin/v1/series/fb_team",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/fb_team",
        )

    @task(1)
    def admin_series_edit_fb(self):
        """
        POST /admin/v1/series/edit_fb
        Summary: Edit football team details
        Requires Auth: False
        """
        request_data = {
            "team_id": "test_fb_team_id_1",  # Placeholder
            "logo_path": f"https://example.com/fb_logo_{int(time.time())}.png",
            "short_code": "BRA",
        }
        self.client.post(
            "/admin/v1/series/edit_fb",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/edit_fb",
        )

    @task(1)
    def admin_series_series_team_add(self):
        """
        POST /admin/v1/series/series_team_add
        Summary: Add or update series team metadata
        Requires Auth: False
        """
        request_data = {
            "type": "cricket",
            "league_id": 1,
            "logo_url": f"https://example.com/series_team_{int(time.time())}.png",
            "short_name": "TST",
        }
        self.client.post(
            "/admin/v1/series/series_team_add",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/series_team_add",
        )

    @task(1)
    def admin_series_ckt_team_list(self):
        """
        POST /admin/v1/series/ckt_team_list
        Summary: Get list of cricket teams
        Requires Auth: False
        """
        request_data = {"name": "Team A"}
        self.client.post(
            "/admin/v1/series/ckt_team_list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/ckt_team_list",
        )

    @task(1)
    def admin_series_fb_team_list(self):
        """
        POST /admin/v1/series/fb_team_list
        Summary: Get list of football teams
        Requires Auth: False
        """
        request_data = {"name": "Team B"}
        self.client.post(
            "/admin/v1/series/fb_team_list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/series/fb_team_list",
        )

    @task(1)
    def admin_setting_create(self):
        """
        POST /admin/v1/setting/create
        Summary: Create or update settings
        Requires Auth: True
        """
        request_data = {
            "title": f"Setting Title {int(time.time())}",
            "content": "<p>Setting content here.</p>",
            "slug": "general-settings",
        }
        self.client.post(
            "/admin/v1/setting/create",
            json=request_data,
            name="POST /admin/v1/setting/create",
        )

    @task(1)
    def admin_setting_view(self):
        """
        GET /admin/v1/setting/view
        Summary: View settings
        Requires Auth: True
        """
        self.client.get("/admin/v1/setting/view", name="GET /admin/v1/setting/view")

    @task(1)
    def admin_statemanger_list(self):
        """
        GET /admin/v1/statemanger/list
        Summary: Get the list of states
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/statemanger/list", name="GET /admin/v1/statemanger/list"
        )

    @task(1)
    def admin_statemanger_edit(self):
        """
        POST /admin/v1/statemanger/edit
        Summary: Edit a state
        Requires Auth: False
        """
        request_data = {
            "id": "64b2fbd05f3e80001c0abcde",  # Placeholder - replace with actual ID
            "status": random.choice([0, 1]),
        }
        self.client.post(
            "/admin/v1/statemanger/edit",
            json=request_data,
            name="POST /admin/v1/statemanger/edit",
        )

    @task(1)
    def admin_subadmin_create(self):
        """
        POST /admin/v1/subadmin/create
        Summary: Create a new subadmin user
        Requires Auth: False
        """
        unique_phone = f"99887766{random.randint(100, 999)}"
        unique_email = f"subadmin_{int(time.time())}@example.com"
        request_data = {
            "name": f"Subadmin User {int(time.time())}",
            "email": unique_email,
            "usertype": 2,
            "country_code": "+91",
            "phone": unique_phone,
            "password": "SubadminPassword1!",
            "module_data": '{"User":true,"Cms_Manager":false,"Matches":true}',
        }
        self.client.post(
            "/admin/v1/subadmin/create",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/subadmin/create",
        )

    @task(1)
    def admin_subadmin_list(self):
        """
        POST /admin/v1/subadmin/list
        Summary: Get a list of all subadmin users
        Requires Auth: True
        """
        request_data = {}  # Assuming empty or specific fields not detailed in snippet
        self.client.post(
            "/admin/v1/subadmin/list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/subadmin/list",
        )

    @task(1)
    def admin_subadmin_active_inactive(self):
        """
        POST /admin/v1/subadmin/active-inactive
        Summary: Toggle active/inactive status of a subadmin user
        Requires Auth: True
        """
        request_data = {}  # Assuming specific fields like 'id' and 'status' are needed
        self.client.post(
            "/admin/v1/subadmin/active-inactive",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/subadmin/active-inactive",
        )

    @task(1)
    def admin_subadmin_update(self):
        """
        POST /admin/v1/subadmin/update
        Summary: Update a subadmin user
        Requires Auth: True
        """
        request_data = (
            {}
        )  # Assuming specific fields like 'id' and updated data are needed
        self.client.post(
            "/admin/v1/subadmin/update",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/subadmin/update",
        )

    @task(1)
    def admin_subadmin_view(self):
        """
        POST /admin/v1/subadmin/view
        Summary: View details of a subadmin user
        Requires Auth: True
        """
        request_data = {}  # Assuming specific fields like 'id' are needed
        self.client.post(
            "/admin/v1/subadmin/view",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/subadmin/view",
        )

    @task(1)
    def admin_tds_list(self):
        """
        POST /admin/v1/tds/list
        Summary: Get list of TDS records
        Requires Auth: True
        """
        request_data = {}  # Assuming empty or specific fields not detailed in snippet
        self.client.post(
            "/admin/v1/tds/list",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/tds/list",
        )

    @task(1)
    def admin_transaction_list(self):
        """
        GET /admin/v1/transaction/list
        Summary: Get list of transactions
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/transaction/list", name="GET /admin/v1/transaction/list"
        )

    @task(1)
    def admin_transaction_filter(self):
        """
        POST /admin/v1/transaction/filter
        Summary: Filter transactions
        Requires Auth: True
        """
        request_data = {
            "startDate": "2024-01-01",
            "endDate": "2024-01-31",
            "status": "completed",
        }
        self.client.post(
            "/admin/v1/transaction/filter",
            json=request_data,
            name="POST /admin/v1/transaction/filter",
        )

    @task(1)
    def admin_transaction_download(self):
        """
        POST /admin/v1/transaction/download
        Summary: Download transaction sheet
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/transaction/download", name="POST /admin/v1/transaction/download"
        )

    @task(1)
    def admin_transaction_get_downloads(self):
        """
        GET /admin/v1/transaction/get-downloads
        Summary: Get downloadable transaction files
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/transaction/get-downloads",
            name="GET /admin/v1/transaction/get-downloads",
        )

    @task(1)
    def admin_transaction_download_file_fileid(self):
        """
        GET /admin/v1/transaction/download-file/{fileId}
        Summary: Download a specific transaction file by ID
        Requires Auth: True
        """
        file_id = "test_file_id"  # Placeholder - replace with actual ID
        self.client.get(
            f"/admin/v1/transaction/download-file/{file_id}",
            name="GET /admin/v1/transaction/download-file/{fileId}",
        )

    @task(1)
    def admin_transaction_files(self):
        """
        DELETE /admin/v1/transaction/files
        Summary: Delete all transaction files
        Requires Auth: True
        """
        self.client.delete(
            "/admin/v1/transaction/files", name="DELETE /admin/v1/transaction/files"
        )

    @task(1)
    def admin_transaction_get_download_affiliates(self):
        """
        GET /admin/v1/transaction/get_download_affiliates
        Summary: Get affiliate download tracking data
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/transaction/get_download_affiliates",
            name="GET /admin/v1/transaction/get_download_affiliates",
        )

    @task(1)
    def admin_transaction_mongo_export_csv(self):
        """
        GET /admin/v1/transaction/mongo_export_csv
        Summary: Export MongoDB data to CSV
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/transaction/mongo_export_csv",
            name="GET /admin/v1/transaction/mongo_export_csv",
        )

    @task(1)
    def admin_transaction_update_user(self):
        """
        GET /admin/v1/transaction/update_user
        Summary: Update user information
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/transaction/update_user",
            name="GET /admin/v1/transaction/update_user",
        )

    @task(1)
    def admin_users_create(self):
        """
        POST /admin/v1/users/create
        Summary: Create a new user
        Requires Auth: False
        """
        unique_phone = f"987654{random.randint(10000, 99999)}"
        unique_email = f"newuser_{int(time.time())}@example.com"
        request_data = {
            "name": f"New User {int(time.time())}",
            "email": unique_email,
            "usertype": 1,
            "country_code": "+91",
            "phone": unique_phone,
            "password": "NewUserPass123!",
            "logintype": "manual",
        }
        self.client.post(
            "/admin/v1/users/create",
            json=request_data,
            name="POST /admin/v1/users/create",
        )

    @task(1)
    def admin_users_logout(self):
        """
        GET /admin/v1/users/logout
        Summary: Logout the authenticated user
        Requires Auth: True
        """
        self.client.get("/admin/v1/users/logout", name="GET /admin/v1/users/logout")

    @task(1)
    def admin_users_forgot(self):
        """
        POST /admin/v1/users/forgot
        Summary: Initiate forgot password process
        Requires Auth: True
        """
        request_data = {
            "phone": "*********0"  # Placeholder - replace with actual phone
        }
        self.client.post(
            "/admin/v1/users/forgot",
            json=request_data,
            name="POST /admin/v1/users/forgot",
        )

    @task(1)
    def admin_users_uservendor_list(self):
        """
        POST /admin/v1/users/uservendorList
        Summary: Get list of user vendors
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/users/uservendorList", name="POST /admin/v1/users/uservendorList"
        )

    @task(1)
    def admin_users_user_list(self):
        """
        POST /admin/v1/users/userList
        Summary: Get list of users
        Requires Auth: True
        """
        self.client.post(
            "/admin/v1/users/userList", name="POST /admin/v1/users/userList"
        )

    @task(1)
    def admin_users_bank_list(self):
        """
        GET /admin/v1/users/bank_list
        Summary: Get list of banks
        Requires Auth: False
        """
        self.client.get(
            "/admin/v1/users/bank_list", name="GET /admin/v1/users/bank_list"
        )

    @task(1)
    def admin_users_update_user(self):
        """
        POST /admin/v1/users/update-user
        Summary: Update a user's status
        Requires Auth: True
        """
        request_data = {"userId": 123, "status": random.choice([0, 1])}  # Placeholder
        self.client.post(
            "/admin/v1/users/update-user",
            json=request_data,
            name="POST /admin/v1/users/update-user",
        )

    @task(1)
    def admin_users_delete(self):
        """
        DELETE /admin/v1/users/delete
        Summary: Delete a user
        Requires Auth: False
        """
        request_data = {"user_id": 101}  # Placeholder
        self.client.delete(
            "/admin/v1/users/delete",
            json=request_data,
            name="DELETE /admin/v1/users/delete",
        )

    @task(1)
    def admin_users_view(self):
        """
        POST /admin/v1/users/view
        Summary: View user details
        Requires Auth: True
        """
        request_data = {"id": 101}  # Placeholder
        self.client.post(
            "/admin/v1/users/view", json=request_data, name="POST /admin/v1/users/view"
        )

    @task(1)
    def admin_users_payment_access(self):
        """
        POST /admin/v1/users/payment_access
        Summary: Get payment access
        Requires Auth: False
        """
        request_data = {"user_id": "test_user_id"}  # Placeholder
        self.client.post(
            "/admin/v1/users/payment_access",
            json=request_data,
            name="POST /admin/v1/users/payment_access",
        )

    @task(1)
    def admin_users_transaction_list(self):
        """
        POST /admin/v1/users/transaction_list
        Summary: Get transaction list
        Requires Auth: False
        """
        request_data = {"user_id": "test_user_id"}  # Placeholder
        self.client.post(
            "/admin/v1/users/transaction_list",
            json=request_data,
            name="POST /admin/v1/users/transaction_list",
        )

    @task(1)
    def admin_users_theme_customization(self):
        """
        POST /admin/v1/users/theme-customization
        Summary: Update theme customization settings for a user
        Requires Auth: True
        """
        request_data = {
            "userId": "test_user_id",  # Placeholder
            "background_color": "#F0F0F0",
            "feature_box_bg": "#FFFFFF",
            "background_light": "#FAFAFA",
            "border_color": "#E0E0E0",
            "circle_color": "#CCCCCC",
            "contest_block_bg": "#F5F5F5",
            "dark_text": "#333333",
            "faq_border": "#BBBBBB",
            "font_secondary": "Arial",
            "light_secondary_color": "#EFEFEF",
            "primary_color": "#4CAF50",
            "progress_color": "#8BC34A",
            "secondary_color": "#FFC107",
            "secondary_dark_color": "#FFA000",
            "table_header": "#EEEEEE",
            "input_bg": "#FFFFFF",
            "font_primary": "Roboto",
        }
        self.client.post(
            "/admin/v1/users/theme-customization",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/users/theme-customization",
        )

    @task(1)
    def admin_users_get_theme_customization(self):
        """
        POST /admin/v1/users/get-theme-customization
        Summary: Get theme customization details for a user
        Requires Auth: True
        """
        request_data = {"userId": "test_user_id"}  # Placeholder
        self.client.post(
            "/admin/v1/users/get-theme-customization",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/users/get-theme-customization",
        )

    @task(1)
    def admin_wallet_list(self):
        """
        GET /admin/v1/wallet/list
        Summary: Get list of wallets
        Requires Auth: True
        """
        self.client.get("/admin/v1/wallet/list", name="GET /admin/v1/wallet/list")

    @task(1)
    def admin_wallet_filter(self):
        """
        POST /admin/v1/wallet/filter
        Summary: Filter wallet records
        Requires Auth: True
        """
        request_data = {}  # Assuming filter criteria are needed here
        self.client.post(
            "/admin/v1/wallet/filter",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/wallet/filter",
        )

    @task(1)
    def admin_wallet_update(self):
        """
        POST /admin/v1/wallet/update
        Summary: Update wallet details
        Requires Auth: True
        """
        request_data = {}  # Assuming update data is needed here
        self.client.post(
            "/admin/v1/wallet/update",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /admin/v1/wallet/update",
        )

    @task(1)
    def admin_wallet_withdraw_list(self):
        """
        GET /admin/v1/wallet/Withdraw/list
        Summary: Get list of wallet withdrawals
        Requires Auth: True
        """
        self.client.get(
            "/admin/v1/wallet/Withdraw/list", name="GET /admin/v1/wallet/Withdraw/list"
        )

    @task(1)
    def app_banners_swaggerscroll(self):
        """
        GET /app/v1/banners/swaggerscroll
        Summary: Get paginated banner scroll data
        Requires Auth: False
        """
        self.client.get(
            "/app/v1/banners/swaggerscroll", name="GET /app/v1/banners/swaggerscroll"
        )

    @task(1)
    def app_cms_category_list(self):
        """
        GET /app/v1/cms/category_list
        Summary: Get list of CMS categories
        Requires Auth: False
        """
        self.client.get(
            "/app/v1/cms/category_list", name="GET /app/v1/cms/category_list"
        )

    @task(1)
    def app_cms_faq_view(self):
        """
        POST /app/v1/cms/faq_view
        Summary: View FAQ list
        Requires Auth: False
        """
        self.client.post("/app/v1/cms/faq_view", name="POST /app/v1/cms/faq_view")

    @task(1)
    def app_cms_web_setting(self):
        """
        GET /app/v1/cms/web-setting
        Summary: Get web settings
        Requires Auth: False
        """
        self.client.get("/app/v1/cms/web-setting", name="GET /app/v1/cms/web-setting")

    @task(1)
    def app_contactus_create(self):
        """
        POST /app/v1/contactus/create
        Summary: Create a new contact us message
        Requires Auth: False
        """
        request_data = {
            "name": f"Contact User {int(time.time())}",
            "email": f"contact_{int(time.time())}@example.com",
            "message": "This is a test contact message from Locust.",
            "subject": "Load Test Inquiry",
            "phone": f"9876543{random.randint(100, 999)}",
        }
        self.client.post(
            "/app/v1/contactus/create",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/contactus/create",
        )

    @task(1)
    def app_match_publish_football_list(self):
        """
        POST /app/v1/match/publish_football_list
        Summary: Get list of active football matches based on status
        Requires Auth: True
        """
        request_data = {"rstatus": 1}
        self.client.post(
            "/app/v1/match/publish_football_list",
            json=request_data,
            name="POST /app/v1/match/publish_football_list",
        )

    @task(1)
    def app_match_cricket_matches_myportfolio(self):
        """
        POST /app/v1/match/cricket/matches/myportfolio
        Summary: Get cricket matches for user's portfolio filtered by status
        Requires Auth: True
        """
        request_data = {"rstatus": 1}
        self.client.post(
            "/app/v1/match/cricket/matches/myportfolio",
            json=request_data,
            name="POST /app/v1/match/cricket/matches/myportfolio",
        )

    @task(1)
    def app_match_football_matches_myportfolio(self):
        """
        POST /app/v1/match/football/matches/myportfolio
        Summary: Get football matches for user's portfolio filtered by status
        Requires Auth: True
        """
        request_data = {"rstatus": 1}
        self.client.post(
            "/app/v1/match/football/matches/myportfolio",
            json=request_data,
            name="POST /app/v1/match/football/matches/myportfolio",
        )

    @task(1)
    def app_match_cricket_series_myportfolio(self):
        """
        POST /app/v1/match/cricket/series/myportfolio
        Summary: Get cricket series for user's portfolio filtered by status
        Requires Auth: True
        """
        request_data = {"status": "active"}
        self.client.post(
            "/app/v1/match/cricket/series/myportfolio",
            json=request_data,
            name="POST /app/v1/match/cricket/series/myportfolio",
        )

    @task(1)
    def app_match_football_series_myportfolio(self):
        """
        POST /app/v1/match/football/series/myportfolio
        Summary: Get football series for user's portfolio filtered by status
        Requires Auth: True
        """
        request_data = {"status": "upcoming"}
        self.client.post(
            "/app/v1/match/football/series/myportfolio",
            json=request_data,
            name="POST /app/v1/match/football/series/myportfolio",
        )

    @task(1)
    def app_match_save_player(self):
        """
        POST /app/v1/match/save_player
        Summary: Save player list for a match with team and role counts
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "match_id": 101,
            "pid": [1, 2, 3],  # Placeholder
            "team_count": {"batsmen": 3, "bowlers": 2},  # Placeholder
            "player_role_count": {"WK": 1, "BAT": 3},  # Placeholder
        }
        self.client.post(
            "/app/v1/match/save_player",
            json=request_data,
            name="POST /app/v1/match/save_player",
        )

    @task(1)
    def app_match_accumulator_team_list(self):
        """
        POST /app/v1/match/accumulator/team_list
        Summary: Get accumulator team list by type and league ID
        Requires Auth: True
        """
        request_data = {"type": "cricket", "league_id": 1}
        self.client.post(
            "/app/v1/match/accumulator/team_list",
            json=request_data,
            name="POST /app/v1/match/accumulator/team_list",
        )

    @task(1)
    def app_match_accumulator_add_team(self):
        """
        POST /app/v1/match/accumulator/add_team
        Summary: Add a team to accumulator with game details
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "gametype": "cricket",
            "gamekey": f"gamekey_{int(time.time())}",
            "data": [
                {"team_id": 1, "pamount": 100, "sharecnt": 10},
                {"team_id": 2, "pamount": 50, "sharecnt": 5},
            ],
        }
        self.client.post(
            "/app/v1/match/accumulator/add_team",
            json=request_data,
            name="POST /app/v1/match/accumulator/add_team",
        )

    @task(1)
    def app_match_accumulator_add_plyaccumulator(self):
        """
        POST /app/v1/match/accumulator/add_plyaccumulator
        Summary: Add player accumulator details for a match
        Requires Auth: True
        """
        request_data = {
            "match_id": 101,
            "gametype": "cricket",
            "gamekey": f"gamekey_player_{int(time.time())}",
            "data": [
                {"pid": 1, "gkamount": 10, "sharecnt": 2},
                {"pid": 2, "gkamount": 5, "sharecnt": 1},
            ],
        }
        self.client.post(
            "/app/v1/match/accumulator/add_plyaccumulator",
            json=request_data,
            name="POST /app/v1/match/accumulator/add_plyaccumulator",
        )

    @task(1)
    def app_match_accumulator_prize_pool_list(self):
        """
        POST /app/v1/match/accumulator/prize_pool_list
        Summary: Get prize pool list for accumulator by league
        Requires Auth: True
        """
        request_data = {"type": "cricket", "league_id": 1}
        self.client.post(
            "/app/v1/match/accumulator/prize_pool_list",
            json=request_data,
            name="POST /app/v1/match/accumulator/prize_pool_list",
        )

    @task(1)
    def app_match_accumulator_add_prize_pool(self):
        """
        POST /app/v1/match/accumulator/add_prize_pool
        Summary: Add prize pool for accumulator match
        Requires Auth: True
        """
        request_data = {
            "match_id": 101,
            "data": [
                {"team_id": 1, "price": 1000, "quality": 1},
                {"team_id": 2, "price": 500, "quality": 2},
            ],
        }
        self.client.post(
            "/app/v1/match/accumulator/add_prize_pool",
            json=request_data,
            name="POST /app/v1/match/accumulator/add_prize_pool",
        )

    @task(1)
    def app_match_player_detail(self):
        """
        POST /app/v1/match/player_detail
        Summary: Get detailed info about a player
        Summary: Get detailed info about a player
        Requires Auth: True
        """
        request_data = {
            "player_id": 1,
            "type": "cricket",
            "seasonId": "season_2024",
            "match_id": "match_101",
            "league_id": 1,
        }
        self.client.post(
            "/app/v1/match/player_detail",
            json=request_data,
            name="POST /app/v1/match/player_detail",
        )

    @task(1)
    def app_match_series_player_detail(self):
        """
        POST /app/v1/match/series_player_detail
        Summary: Get detailed info about a series player
        Requires Auth: True
        """
        request_data = {"player_id": 1}
        self.client.post(
            "/app/v1/match/series_player_detail",
            json=request_data,
            name="POST /app/v1/match/series_player_detail",
        )

    @task(1)
    def app_match_save_series_player(self):
        """
        POST /app/v1/match/save_series_player
        Summary: Save players for a series
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "league_id": 1,
            "pid": [1, 2, 3],
            "is_substitue": 0,
            "team_count": {"batsmen": 5},
            "team_no": 1,
            "contest_id": "series_contest_1",
        }
        self.client.post(
            "/app/v1/match/save_series_player",
            json=request_data,
            name="POST /app/v1/match/save_series_player",
        )

    @task(1)
    def app_match_add_substitute_player_series(self):
        """
        POST /app/v1/match/add_substitute_player_series
        Summary: Add substitute player to series
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "league_id": 1,
            "pid": [4],
            "is_substitue": 1,
            "team_count": {"substitutes": 1},
            "team_no": 1,
            "uteamid": "user_team_xyz",
        }
        self.client.post(
            "/app/v1/match/add_substitute_player_series",
            json=request_data,
            name="POST /app/v1/match/add_substitute_player_series",
        )

    @task(1)
    def app_match_sereis_player_list(self):
        """
        POST /app/v1/match/sereis_player_list
        Summary: Get series player list
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "league_id": 1,
            "contest_id": "series_contest_1",
        }
        self.client.post(
            "/app/v1/match/sereis_player_list",
            json=request_data,
            name="POST /app/v1/match/sereis_player_list",
        )

    @task(1)
    def app_match_cricket_team(self):
        """
        POST /app/v1/match/cricket/team
        Summary: Get cricket team list for user
        Requires Auth: True
        """
        request_data = {"match_id": 101, "poolid": "pool_id_abc", "type": "user_team"}
        self.client.post(
            "/app/v1/match/cricket/team",
            json=request_data,
            name="POST /app/v1/match/cricket/team",
        )

    @task(1)
    def app_match_football_team(self):
        """
        POST /app/v1/match/football/team
        Summary: Get football team list for user
        Requires Auth: True
        """
        request_data = {"match_id": 101, "poolid": "pool_id_def", "type": "user_team"}
        self.client.post(
            "/app/v1/match/football/team",
            json=request_data,
            name="POST /app/v1/match/football/team",
        )

    @task(1)
    def app_match_cricket_seriesteam(self):
        """
        POST /app/v1/match/cricket/seriesteam
        Summary: Get cricket series team list
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "poolid": "series_pool_id_gh",
            "contest_id": "series_contest_xyz",
        }
        self.client.post(
            "/app/v1/match/cricket/seriesteam",
            json=request_data,
            name="POST /app/v1/match/cricket/seriesteam",
        )

    @task(1)
    def app_match_football_seriesteam(self):
        """
        POST /app/v1/match/football/seriesteam
        Summary: Get football series team list
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "poolid": "series_pool_id_ij",
            "contest_id": "series_contest_uvw",
        }
        self.client.post(
            "/app/v1/match/football/seriesteam",
            json=request_data,
            name="POST /app/v1/match/football/seriesteam",
        )

    @task(1)
    def app_match_edit_match_player_list(self):
        """
        POST /app/v1/match/edit_match_player_list
        Summary: Edit match player list
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "uteamid": "user_team_abc",
            "match_id": 101,
            "pid": [1, 2, 5],
            "team_count": {"batsmen": 4, "bowlers": 1},
            "player_role_count": {"WK": 1, "BAT": 4},
            "team_no": 1,
        }
        self.client.post(
            "/app/v1/match/edit_match_player_list",
            json=request_data,
            name="POST /app/v1/match/edit_match_player_list",
        )

    @task(1)
    def app_match_edit_series_player_list(self):
        """
        POST /app/v1/match/edit_series_player_list
        Summary: Edit series player list
        Requires Auth: True
        """
        request_data = {
            "type": "cricket",
            "uteamid": "user_series_team_xyz",
            "league_id": 1,
            "pid": [1, 2, 3],
            "team_count": {"players": 11},
            "is_substitue": 0,
            "team_no": 1,
        }
        self.client.post(
            "/app/v1/match/edit_series_player_list",
            json=request_data,
            name="POST /app/v1/match/edit_series_player_list",
        )

    @task(1)
    def app_match_cricket_user_match_player_list(self):
        """
        POST /app/v1/match/cricket/user_match_player_list
        Summary: Get cricket user match player list
        Requires Auth: True
        """
        request_data = {
            "uteamid": "user_team_abc",
            "smtype": "regular",
            "type": "cricket",
        }
        self.client.post(
            "/app/v1/match/cricket/user_match_player_list",
            json=request_data,
            name="POST /app/v1/match/cricket/user_match_player_list",
        )

    @task(1)
    def app_match_football_user_match_player_list(self):
        """
        POST /app/v1/match/football/user_match_player_list
        Summary: Get football user match player list
        Requires Auth: True
        """
        request_data = {
            "uteamid": "user_team_xyz",
            "smtype": "regular",
            "type": "football",
        }
        self.client.post(
            "/app/v1/match/football/user_match_player_list",
            json=request_data,
            name="POST /app/v1/match/football/user_match_player_list",
        )

    @task(1)
    def app_match_cricket_match_clone_player_list(self):
        """
        POST /app/v1/match/cricket/match/clone_player_list
        Summary: Clone cricket match player list by user team ID and team number
        Requires Auth: True
        """
        request_data = {"uteamid": "user_team_clone_1", "team_no": 1}
        self.client.post(
            "/app/v1/match/cricket/match/clone_player_list",
            json=request_data,
            name="POST /app/v1/match/cricket/match/clone_player_list",
        )

    @task(1)
    def app_match_football_match_clone_player_list(self):
        """
        POST /app/v1/match/football/match/clone_player_list
        Summary: Clone football match player list by user team ID and team number
        Requires Auth: True
        """
        request_data = {"uteamid": "user_team_clone_2", "team_no": 1}
        self.client.post(
            "/app/v1/match/football/match/clone_player_list",
            json=request_data,
            name="POST /app/v1/match/football/match/clone_player_list",
        )

    @task(1)
    def app_match_cricket_series_clone_player_list(self):
        """
        POST /app/v1/match/cricket/series/clone_player_list
        Summary: Clone cricket series player list by user team ID
        Requires Auth: True
        """
        request_data = {"uteamid": "user_series_clone_1"}
        self.client.post(
            "/app/v1/match/cricket/series/clone_player_list",
            json=request_data,
            name="POST /app/v1/match/cricket/series/clone_player_list",
        )

    @task(1)
    def app_match_football_series_clone_player_list(self):
        """
        POST /app/v1/match/football/series/clone_player_list
        Summary: Clone football series player list by user team ID
        Requires Auth: True
        """
        request_data = {"uteamid": "user_series_clone_2"}
        self.client.post(
            "/app/v1/match/football/series/clone_player_list",
            json=request_data,
            name="POST /app/v1/match/football/series/clone_player_list",
        )

    @task(1)
    def app_match_fb_live_score(self):
        """
        POST /app/v1/match/fb_live_score
        Summary: Get live football match score
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/match/fb_live_score", name="POST /app/v1/match/fb_live_score"
        )

    @task(1)
    def app_match_commentary_score_list(self):
        """
        POST /app/v1/match/commentary_score_list
        Summary: Get commentary score list for a match
        Requires Auth: True
        """
        request_data = {"match_id": 101, "type": "ckt"}
        self.client.post(
            "/app/v1/match/commentary_score_list",
            json=request_data,
            name="POST /app/v1/match/commentary_score_list",
        )

    @task(1)
    def app_match_team_profile_detail(self):
        """
        POST /app/v1/match/team_profile_detail
        Summary: Get detailed profile of a team
        Requires Auth: True
        """
        request_data = {"gametype": "cricket", "team_id": 1}
        self.client.post(
            "/app/v1/match/team_profile_detail",
            json=request_data,
            name="POST /app/v1/match/team_profile_detail",
        )

    @task(1)
    def app_match_series_team_stats_list(self):
        """
        POST /app/v1/match/series_team_stats_list
        Summary: Get team stats list for a series
        Requires Auth: True
        """
        request_data = {"cid": 1, "type": "cricket"}  # Series ID
        self.client.post(
            "/app/v1/match/series_team_stats_list",
            json=request_data,
            name="POST /app/v1/match/series_team_stats_list",
        )

    @task(1)
    def app_match_series_match_list(self):
        """
        POST /app/v1/match/series_match_list
        Summary: Get list of matches in a series
        Requires Auth: True
        """
        request_data = {"cid": 1, "type": "cricket"}  # Series ID
        self.client.post(
            "/app/v1/match/series_match_list",
            json=request_data,
            name="POST /app/v1/match/series_match_list",
        )

    @task(1)
    def app_match_update_substitute_player_series(self):
        """
        POST /app/v1/match/update_substitute_player_series
        Summary: Update substitute players in a series
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "pid": [5, 6],
            "uteamid": "user_team_xyz",
            "match_id": 101,
            "contest_id": "contest_abc",
            "type": "cricket",
        }
        self.client.post(
            "/app/v1/match/update_substitute_player_series",
            json=request_data,
            name="POST /app/v1/match/update_substitute_player_series",
        )

    @task(1)
    def app_notification_list(self):
        """
        POST /app/v1/notification/list
        Summary: Get list of notifications
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/notification/list", name="POST /app/v1/notification/list"
        )

    @task(1)
    def app_payment_create_order(self):
        """
        POST /app/v1/payment/createOrder
        Summary: Create a payment order
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/payment/createOrder", name="POST /app/v1/payment/createOrder"
        )

    @task(1)
    def app_payment_create_order_in_trans(self):
        """
        POST /app/v1/payment/createOrderInTrans
        Summary: Create a payment order within a transaction
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/payment/createOrderInTrans",
            name="POST /app/v1/payment/createOrderInTrans",
        )

    @task(1)
    def app_payment_verify_order(self):
        """
        POST /app/v1/payment/verifyOrder
        Summary: Verify a payment order
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/payment/verifyOrder", name="POST /app/v1/payment/verifyOrder"
        )

    @task(1)
    def app_payment_phonepepay(self):
        """
        POST /app/v1/payment/phonepepay
        Summary: Initiate PhonePe payment
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/payment/phonepepay", name="POST /app/v1/payment/phonepepay"
        )

    @task(1)
    def app_payment_responsephonepe(self):
        """
        POST /app/v1/payment/responsephonepe
        Summary: Handle PhonePe payment response
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/payment/responsephonepe",
            name="POST /app/v1/payment/responsephonepe",
        )

    @task(1)
    def app_payment_phonepepay_mobile(self):
        """
        POST /app/v1/payment/phonepepay/mobile
        Summary: Initiate PhonePe payment on mobile
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/payment/phonepepay/mobile",
            name="POST /app/v1/payment/phonepepay/mobile",
        )

    @task(1)
    def app_payment_responsephonepe_mobile(self):
        """
        POST /app/v1/payment/responsephonepe/mobile
        Summary: Handle PhonePe mobile payment response
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/payment/responsephonepe/mobile",
            name="POST /app/v1/payment/responsephonepe/mobile",
        )

    @task(1)
    def app_pool_matches_cricket_pool_contest_list(self):
        """
        POST /app/v1/pool/matches/cricket/pool_contest_list
        Summary: Get list of cricket pool contests filtered by various parameters
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "uptojoinmin": 10,
            "uptojoinmax": 100,
            "entrymax": 500,
            "emtrymin": 100,
            "prizepoolmax": 10000,
            "prizepoolmin": 1000,
            "gurantee": 1,
            "contestType": "free",
            "contestid": "CONTEST123",
            "isprivate": 0,
        }
        self.client.post(
            "/app/v1/pool/matches/cricket/pool_contest_list",
            json=request_data,
            name="POST /app/v1/pool/matches/cricket/pool_contest_list",
        )

    @task(1)
    def app_pool_filter(self):
        """
        POST /app/v1/pool/filter
        Summary: Filter cricket contests by match ID and other criteria
        Requires Auth: True
        """
        request_data = {"match_id": 123}
        self.client.post(
            "/app/v1/pool/filter", json=request_data, name="POST /app/v1/pool/filter"
        )

    @task(1)
    def app_pool_matches_football_pool_contest_list(self):
        """
        POST /app/v1/pool/matches/football/pool_contest_list
        Summary: Get football pool contest list with filters
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "uptojoinmin": 10,
            "uptojoinmax": 100,
            "entrymax": 500,
            "emtrymin": 100,
            "prizepoolmax": 10000,
            "prizepoolmin": 1000,
            "gurantee": 1,
            "contestType": "free",
            "contestid": "CONTEST123",
            "isprivate": 0,
        }
        self.client.post(
            "/app/v1/pool/matches/football/pool_contest_list",
            json=request_data,
            name="POST /app/v1/pool/matches/football/pool_contest_list",
        )

    @task(1)
    def app_pool_series_cricket_pool_contest_list(self):
        """
        POST /app/v1/pool/series/cricket/pool_contest_list
        Summary: Get cricket series pool contest list filtered by league and status
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "status": "active",
            "contest_id": "SERIESCONTEST123",
        }
        self.client.post(
            "/app/v1/pool/series/cricket/pool_contest_list",
            json=request_data,
            name="POST /app/v1/pool/series/cricket/pool_contest_list",
        )

    @task(1)
    def app_pool_series_football_pool_contest_list(self):
        """
        POST /app/v1/pool/series/football/pool_contest_list
        Summary: Get football series pool contest list filtered by league and status
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "status": "upcoming",
            "contest_id": "FBSERIESCONTEST123",
        }
        self.client.post(
            "/app/v1/pool/series/football/pool_contest_list",
            json=request_data,
            name="POST /app/v1/pool/series/football/pool_contest_list",
        )

    @task(1)
    def app_pool_match_cricket_pool_detail(self):
        """
        POST /app/v1/pool/match/cricket/pool_detail
        Summary: Get cricket match pool details by match and pool ID
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "pool_id": "pool_abc",
            "status": 1,
            "userid": 101,
        }
        self.client.post(
            "/app/v1/pool/match/cricket/pool_detail",
            json=request_data,
            name="POST /app/v1/pool/match/cricket/pool_detail",
        )

    @task(1)
    def app_pool_match_football_pool_detail(self):
        """
        POST /app/v1/pool/match/football/pool_detail
        Summary: Get football match pool details by match and pool ID
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "pool_id": "pool_def",
            "status": 1,
            "userid": 101,
        }
        self.client.post(
            "/app/v1/pool/match/football/pool_detail",
            json=request_data,
            name="POST /app/v1/pool/match/football/pool_detail",
        )

    @task(1)
    def app_pool_series_cricket_pool_detail(self):
        """
        POST /app/v1/pool/series/cricket/pool_detail
        Summary: Get cricket series pool details by league and pool ID
        Requires Auth: True
        """
        request_data = {"league_id": 1, "pool_id": "series_pool_abc"}
        self.client.post(
            "/app/v1/pool/series/cricket/pool_detail",
            json=request_data,
            name="POST /app/v1/pool/series/cricket/pool_detail",
        )

    @task(1)
    def app_pool_series_football_pool_detail(self):
        """
        POST /app/v1/pool/series/football/pool_detail
        Summary: Get football series pool details by league and pool ID
        Requires Auth: True
        """
        request_data = {"league_id": 1, "pool_id": "series_pool_def"}
        self.client.post(
            "/app/v1/pool/series/football/pool_detail",
            json=request_data,
            name="POST /app/v1/pool/series/football/pool_detail",
        )

    @task(1)
    def app_pool_confirm_join_pre(self):
        """
        POST /app/v1/pool/confirm_join_pre
        Summary: Preview prize details before joining a pool
        Requires Auth: True
        """
        request_data = {"pool_id": "pool_id_xyz", "user_id": "user_id_123"}
        self.client.post(
            "/app/v1/pool/confirm_join_pre",
            json=request_data,
            name="POST /app/v1/pool/confirm_join_pre",
        )

    @task(1)
    def app_pool_matches_cricket_mypool(self):
        """
        POST /app/v1/pool/matches/cricket/mypool
        Summary: Get user's cricket pool contests for a match
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "uptojoinmin": 1,
            "uptojoinmax": 10,
            "entrymax": 500,
            "emtrymin": 10,
            "prizepoolmax": 10000,
            "prizepoolmin": 100,
            "gurantee": 0,
            "contestType": "paid",
            "contestid": "MYCONTEST456",
            "isprivate": 0,
        }
        self.client.post(
            "/app/v1/pool/matches/cricket/mypool",
            json=request_data,
            name="POST /app/v1/pool/matches/cricket/mypool",
        )

    @task(1)
    def app_pool_matches_football_mypool(self):
        """
        POST /app/v1/pool/matches/football/mypool
        Summary: Get user's football pool contests for a match
        Requires Auth: True
        """
        request_data = {
            "match_id": 123,
            "uptojoinmin": 1,
            "uptojoinmax": 10,
            "entrymax": 500,
            "emtrymin": 10,
            "prizepoolmax": 10000,
            "prizepoolmin": 100,
            "gurantee": 0,
            "contestType": "paid",
            "contestid": "MYFB_CONTEST456",
            "isprivate": 0,
        }
        self.client.post(
            "/app/v1/pool/matches/football/mypool",
            json=request_data,
            name="POST /app/v1/pool/matches/football/mypool",
        )

    @task(1)
    def app_pool_series_cricket_mypool(self):
        """
        POST /app/v1/pool/series/cricket/mypool
        Summary: Get user's cricket pool contests for a series
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "status": "active",
            "contest_id": "MYSERIESCONTEST1",
        }
        self.client.post(
            "/app/v1/pool/series/cricket/mypool",
            json=request_data,
            name="POST /app/v1/pool/series/cricket/mypool",
        )

    @task(1)
    def app_pool_series_football_mypool(self):
        """
        POST /app/v1/pool/series/football/mypool
        Summary: Get user's football pool contests for a series
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "status": "upcoming",
            "contest_id": "MYFBSERIESCONTEST1",
        }
        self.client.post(
            "/app/v1/pool/series/football/mypool",
            json=request_data,
            name="POST /app/v1/pool/series/football/mypool",
        )

    @task(1)
    def app_pool_privatecontest_contestsize(self):
        """
        POST /app/v1/pool/privatecontest/contestsize
        Summary: Get list of private contest size options
        Requires Auth: True
        """
        request_data = {"contest_size": 10}
        self.client.post(
            "/app/v1/pool/privatecontest/contestsize",
            json=request_data,
            name="POST /app/v1/pool/privatecontest/contestsize",
        )

    @task(1)
    def app_pool_match_poolprizebreak(self):
        """
        POST /app/v1/pool/match/poolprizebreak
        Summary: Save prize break information for a match pool
        Requires Auth: False
        """
        request_data = (
            {}
        )  # Example body not explicitly given in swagger, using empty object
        self.client.post(
            "/app/v1/pool/match/poolprizebreak",
            json=request_data,
            name="POST /app/v1/pool/match/poolprizebreak",
        )

    @task(1)
    def app_pool_sereis_privatecontest_save(self):
        """
        POST /app/v1/pool/sereis/privatecontest/save
        Summary: Save a private contest for a series
        Requires Auth: True
        """
        request_data = {
            "league_id": 1,
            "privatename": f"Series Private Contest {int(time.time())}",
            "maxteams": random.randint(2, 50),
            "joinfee": random.randint(10, 500),
            "winners": random.randint(1, 5),
            "s": 0,
            "m": 0,
            "date_start": "2025-01-01",
            "date_end": "2025-01-31",
            "gtype": "cricket",
            "totalwinamt": random.randint(100, 5000),
            "poolpb": [
                {"pmin": 1, "pmax": 1, "pamount": 1000},
                {"pmin": 2, "pmax": 3, "pamount": 500},
            ],
        }
        self.client.post(
            "/app/v1/pool/sereis/privatecontest/save",
            json=request_data,
            name="POST /app/v1/pool/sereis/privatecontest/save",
        )

    @task(1)
    def app_pool_join_pool_series(self):
        """
        POST /app/v1/pool/join_pool_series
        Summary: Join a pool contest for a series
        Requires Auth: True
        """
        request_data = {
            "poolid": "series_pool_abc",
            "league_id": 1,
            "gametype": "cricket",
            "uteamid": "user_series_team_123",
        }
        self.client.post(
            "/app/v1/pool/join_pool_series",
            json=request_data,
            name="POST /app/v1/pool/join_pool_series",
        )

    @task(1)
    def app_pool_confirm_join_pre_series(self):
        """
        POST /app/v1/pool/confirm_join_pre_series
        Summary: Confirm join preview for a series pool contest
        Requires Auth: True
        """
        request_data = {"pool_id": "series_pool_xyz", "user_id": "user_id_456"}
        self.client.post(
            "/app/v1/pool/confirm_join_pre_series",
            json=request_data,
            name="POST /app/v1/pool/confirm_join_pre_series",
        )

    @task(1)
    def app_pool_matches_user_team_player_list(self):
        """
        POST /app/v1/pool/matches/user_team_player_list
        Summary: Get player list of a user's team for a match
        Requires Auth: False
        """
        request_data = {
            "match_id": 123,
            "uteamid": "team_456",
            "gametype": "cricket",
            "type": "fantasy",
            "contest_id": "contest_789",
        }
        self.client.post(
            "/app/v1/pool/matches/user_team_player_list",
            json=request_data,
            name="POST /app/v1/pool/matches/user_team_player_list",
        )

    @task(1)
    def app_users_social_login(self):
        """
        POST /app/v1/users/social_login
        Summary: User social login
        Requires Auth: False
        """
        request_data = {
            "usertype": 1,
            "country_code": "+91",
            "phone": "9876543210",
            "email": f"socialuser_{int(time.time())}@example.com",
            "socialid": f"social_id_{int(time.time())}",
            "socialtype": 1,
            "profilepic": "https://example.com/social_profile.jpg",
        }
        self.client.post(
            "/app/v1/users/social_login",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/social_login",
        )

    @task(1)
    def app_users_send_app_link(self):
        """
        POST /app/v1/users/send_app_link
        Summary: Send app link to user
        Requires Auth: False
        """
        request_data = {
            "country_code": "+91",
            "phone": f"999888{random.randint(1000, 9999)}",
        }
        self.client.post(
            "/app/v1/users/send_app_link",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/send_app_link",
        )

    @task(1)
    def app_users_update_token(self):
        """
        POST /app/v1/users/update_token
        Summary: Update user's device token
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/users/update_token", name="POST /app/v1/users/update_token"
        )

    @task(1)
    def app_users_resend(self):
        """
        POST /app/v1/users/resend
        Summary: Resend OTP to user phone
        Requires Auth: False
        """
        request_data = {"country_code": "+91", "phone": "9988771234"}  # Placeholder
        self.client.post(
            "/app/v1/users/resend",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/resend",
        )

    @task(1)
    def app_users_forgot(self):
        """
        POST /app/v1/users/forgot
        Summary: Forgot password - send OTP
        Requires Auth: False
        """
        request_data = {"country_code": "+91", "phone": "9988771234"}  # Placeholder
        self.client.post(
            "/app/v1/users/forgot",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/forgot",
        )

    @task(1)
    def app_users_verifyotp(self):
        """
        POST /app/v1/users/verifyotp
        Summary: Verify OTP for user
        Requires Auth: False
        """
        request_data = {
            "country_code": "+91",
            "phone": "7021216784",
            "otp": "102307",
            "isVerifed": True,
            "rdevicetype": "web",
            "ip": "***********",
            "timezone": "Asia/Kolkata",
        }
        self.client.post(
            "/app/v1/users/verifyotp",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/verifyotp",
        )

    @task(1)
    def app_users_reset(self):
        """
        POST /app/v1/users/reset
        Summary: Reset user password
        Requires Auth: True
        """
        request_data = {"password": "NewSecurePassword123!"}
        self.client.post(
            "/app/v1/users/reset",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/reset",
        )

    @task(1)
    def app_users_verifyemail(self):
        """
        POST /app/v1/users/verifyemail
        Summary: Verify user email
        Requires Auth: False
        """
        request_data = {"email": f"verify_email_{int(time.time())}@example.com"}
        self.client.post(
            "/app/v1/users/verifyemail",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/verifyemail",
        )

    @task(1)
    def app_users_completeprofile(self):
        """
        POST /app/v1/users/completeprofile
        Summary: Complete user profile
        Requires Auth: True
        """
        request_data = {
            "name": f"Full Name {int(time.time())}",
            "dob": "2000-01-01",
            "gender": "male",
            "address": "123 Test St",
            "cityid": 1,
            "stateid": 1,
            "pincode": 123456,
        }
        self.client.post(
            "/app/v1/users/completeprofile",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/completeprofile",
        )

    @task(1)
    def app_users_update_profile(self):
        """
        POST /app/v1/users/update_profile
        Summary: Update user profile
        Requires Auth: True
        """
        request_data = {
            "userid": "test_user_id",  # Placeholder
            "name": f"Updated Name {int(time.time())}",
            "dob": "1990-05-15",
            "gender": "female",
            "address": "456 Updated Ave",
            "cityid": 2,
            "stateid": 2,
            "pincode": "654321",
            "profilepic": f"https://example.com/updated_profile_{int(time.time())}.jpg",
        }
        self.client.post(
            "/app/v1/users/update_profile",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/update_profile",
        )

    @task(1)
    def app_users_city(self):
        """
        POST /app/v1/users/city
        Summary: Get list of cities by state
        Requires Auth: False
        """
        request_data = {"state": "Maharashtra"}
        self.client.post(
            "/app/v1/users/city",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/city",
        )

    @task(1)
    def app_users_state(self):
        """
        POST /app/v1/users/state
        Summary: Get list of states by country
        Requires Auth: False
        """
        request_data = {"country": "India"}
        self.client.post(
            "/app/v1/users/state",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/state",
        )

    @task(1)
    def app_users_verification(self):
        """
        POST /app/v1/users/verification
        Summary: Verify user details based on type
        Requires Auth: True
        """
        # This endpoint uses multipart/form-data with a `oneOf` schema.
        # Example for "Indian" type with optional file upload.
        request_data = {
            "userid": 101,  # Placeholder
            "type": "Indian",
            "panname": f"Test Pan Name {int(time.time())}",
            "dob": "1995-01-01",
        }
        # Simulate file upload for 'panimage'
        files_data = {
            "panimage": ("pan_dummy.jpg", b"dummy_image_content", "image/jpeg")
        }
        self.client.post(
            "/app/v1/users/verification",
            data=request_data,
            files=files_data,
            name="POST /app/v1/users/verification",
        )

    @task(1)
    def app_users_bankverification(self):
        """
        POST /app/v1/users/bankverification
        Summary: Verify bank details of user
        Requires Auth: True
        """
        request_data = {
            "userid": 101,  # Placeholder
            "bankname": "Test Bank",
            "ifsccode": "TEST0000123",
            "acholdername": f"Account Holder {int(time.time())}",
            "acno": f"*********{random.randint(10,99)}",
            "upi": f"testupi{int(time.time())}@bank",
        }
        self.client.post(
            "/app/v1/users/bankverification",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/bankverification",
        )

    @task(1)
    def app_users_upiverification(self):
        """
        POST /app/v1/users/upiverification
        Summary: Verify UPI details of user
        Requires Auth: True
        """
        request_data = {
            "userid": 101,  # Placeholder
            "upi": f"testupi_verify_{int(time.time())}@bank",
        }
        self.client.post(
            "/app/v1/users/upiverification",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/upiverification",
        )

    @task(1)
    def app_users_view(self):
        """
        GET /app/v1/users/view
        Summary: View user transaction details
        Requires Auth: True
        """
        self.client.get("/app/v1/users/view", name="GET /app/v1/users/view")

    @task(1)
    def app_users_persona_verify(self):
        """
        POST /app/v1/users/persona_verify
        Summary: Verify personal details of a user
        Requires Auth: True
        """
        request_data = {
            "persona_id": "persona_id_123",  # Placeholder
            "persona_status": "verified",
            "id_data": {"document_type": "Aadhaar", "document_number": "*********012"},
        }
        self.client.post(
            "/app/v1/users/persona_verify",
            json=request_data,
            name="POST /app/v1/users/persona_verify",
        )

    @task(1)
    def app_users_sure_verify(self):
        """
        POST /app/v1/users/sure_verify
        Summary: SurePay verification of a user
        Requires Auth: True
        """
        request_data = {
            "type": "mobile",
            "idno": "9876543210",  # Placeholder
            "client_id": "client_abc",
            "otp": "123456",
        }
        self.client.post(
            "/app/v1/users/sure_verify",
            data=request_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            name="POST /app/v1/users/sure_verify",
        )

    @task(1)
    def app_users_cricket_uc(self):
        """
        POST /app/v1/users/cricket/uc
        Summary: Get upcoming cricket matches
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/users/cricket/uc", name="POST /app/v1/users/cricket/uc"
        )

    @task(1)
    def app_users_cricket_players(self):
        """
        POST /app/v1/users/cricket/players
        Summary: Get list of players for a cricket match
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/users/cricket/players", name="POST /app/v1/users/cricket/players"
        )

    @task(1)
    def app_users_cricket_scores(self):
        """
        POST /app/v1/users/cricket/scores
        Summary: Get detailed scores of a cricket match
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/users/cricket/scores", name="POST /app/v1/users/cricket/scores"
        )

    @task(1)
    def app_users_cricket_scores_short(self):
        """
        POST /app/v1/users/cricket/scores/short
        Summary: Get short score summary
        Requires Auth: False
        """
        self.client.post(
            "/app/v1/users/cricket/scores/short",
            name="POST /app/v1/users/cricket/scores/short",
        )

    @task(1)
    def app_users_wallet_withdraw_request(self):
        """
        POST /app/v1/users/wallet/withdraw_request
        Summary: Request wallet withdrawal
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/users/wallet/withdraw_request",
            name="POST /app/v1/users/wallet/withdraw_request",
        )

    @task(1)
    def app_users_refer_earn(self):
        """
        POST /app/v1/users/refer_earn
        Summary: Calculate referral earnings
        Requires Auth: True
        """
        self.client.post(
            "/app/v1/users/refer_earn", name="POST /app/v1/users/refer_earn"
        )

    @task(1)
    def app_users_refer_earn_user(self):
        """
        GET /app/v1/users/refer_earn_user
        Summary: Get referral earnings of a user
        Requires Auth: True
        """
        self.client.get(
            "/app/v1/users/refer_earn_user", name="GET /app/v1/users/refer_earn_user"
        )

    @task(1)
    def app_users_verify_email_otp(self):
        """
        POST /app/v1/users/verify-email-otp
        Summary: Verify OTP sent to email
        Requires Auth: True
        """
        request_data = {
            "email": f"verify_otp_{int(time.time())}@example.com",
            "otp": 123456,
        }
        self.client.post(
            "/app/v1/users/verify-email-otp",
            json=request_data,
            name="POST /app/v1/users/verify-email-otp",
        )

    @task(1)
    def app_users_a_id(self):
        """
        GET /app/v1/users/a/{id}
        Summary: Count API hit by ID
        Requires Auth: False
        """
        id_val = "test_id_123"  # Placeholder
        self.client.get(f"/app/v1/users/a/{id_val}", name="GET /app/v1/users/a/{id}")

    @task(1)
    def app_users_email_test(self):
        """
        GET /app/v1/users/email_test
        Summary: Send test email
        Requires Auth: False
        """
        self.client.get("/app/v1/users/email_test", name="GET /app/v1/users/email_test")

    @task(1)
    def app_users_signin(self):
        """
        GET /app/v1/users/signin
        Summary: Authenticate vendor using user ID and API key
        Requires Auth: False
        """
        userid_param = "vendor_user_id"  # Placeholder
        apikey_param = "vendor_api_key"  # Placeholder
        self.client.get(
            f"/app/v1/users/signin?userid={userid_param}&apikey={apikey_param}",
            name="GET /app/v1/users/signin",
        )

    @task(1)
    def socket_match_myfixliveresult(self):
        """
        POST /socket/v1/match/myfixliveresult
        Summary: Fix live Result
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/socket/v1/match/myfixliveresult",
            json=request_data,
            name="POST /socket/v1/match/myfixliveresult",
        )

    @task(1)
    def socket_player_playerlist(self):
        """
        POST /socket/v1/player/playerlist
        Summary: View player list
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/socket/v1/player/playerlist",
            json=request_data,
            name="POST /socket/v1/player/playerlist",
        )

    @task(1)
    def socket_pool_pooldetailseries(self):
        """
        POST /socket/v1/pool/pooldetailseries
        Summary: Get pool details for a specific series
        Requires Auth: True
        """
        request_data = {"series_id": "67890"}  # Placeholder
        self.client.post(
            "/socket/v1/pool/pooldetailseries",
            json=request_data,
            name="POST /socket/v1/pool/pooldetailseries",
        )

    @task(1)
    def socket_pool_poolmatchcricketlist(self):
        """
        POST /socket/v1/pool/poolmatchcricketlist
        Summary: Get match cricket pool contest list
        Requires Auth: True
        """
        request_data = {"match_id": "test_match_id"}  # Placeholder
        self.client.post(
            "/socket/v1/pool/poolmatchcricketlist",
            json=request_data,
            name="POST /socket/v1/pool/poolmatchcricketlist",
        )

    @task(1)
    def socket_pool_poolmatchfootballlist(self):
        """
        POST /socket/v1/pool/poolmatchfootballlist
        Summary: Get match football pool contest list
        Requires Auth: True
        """
        request_data = {"match_id": "test_football_match_id"}  # Placeholder
        self.client.post(
            "/socket/v1/pool/poolmatchfootballlist",
            json=request_data,
            name="POST /socket/v1/pool/poolmatchfootballlist",
        )

    @task(1)
    def socket_pool_poolcricketlist(self):
        """
        POST /socket/v1/pool/poolcricketlist
        Summary: Get cricket pool contest list for a series
        Requires Auth: True
        """
        request_data = {"league_id": "test_league_id"}  # Placeholder
        self.client.post(
            "/socket/v1/pool/poolcricketlist",
            json=request_data,
            name="POST /socket/v1/pool/poolcricketlist",
        )

    @task(1)
    def socket_pool_poolfootballlist(self):
        """
        POST /socket/v1/pool/poolfootballlist
        Summary: Get football pool contest list for a series
        Requires Auth: True
        """
        request_data = {"league_id": "test_football_league_id"}  # Placeholder
        self.client.post(
            "/socket/v1/pool/poolfootballlist",
            json=request_data,
            name="POST /socket/v1/pool/poolfootballlist",
        )

    @task(1)
    def socket_accmulator_poolpzaccmulator(self):
        """
        POST /socket/v1/accmulator/poolpzaccmulator
        Summary: Process pool PZ accumulator data
        Requires Auth: True
        """
        request_data = {}
        self.client.post(
            "/socket/v1/accmulator/poolpzaccmulator",
            json=request_data,
            name="POST /socket/v1/accmulator/poolpzaccmulator",
        )

    @task(1)
    def socket_cache_cachestorageset(self):
        """
        POST /socket/v1/cache/cachestorageset
        Summary: Set data in socket cache storage
        Requires Auth: True
        """
        request_data = {
            "key": "match_list",
            "value": {"data": "some_data", "timestamp": int(time.time())},
        }
        self.client.post(
            "/socket/v1/cache/cachestorageset",
            json=request_data,
            name="POST /socket/v1/cache/cachestorageset",
        )
