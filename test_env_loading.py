#!/usr/bin/env python3
"""
Test script to verify that .env file is properly loaded and used in Locust.
This validates that environment variables are correctly loaded from the .env file.
"""

import os
import sys
from locustfile import AdminUser, EndUser

def test_env_file_loading():
    """Test that .env file variables are properly loaded."""
    
    print("=" * 60)
    print("TESTING .ENV FILE LOADING")
    print("=" * 60)
    
    # Test admin credentials
    print("\n🔍 Admin Credentials from .env:")
    print("-" * 40)
    
    login_email = os.getenv('LOGIN_EMAIL')
    login_password = os.getenv('LOGIN_PASSWORD')
    
    print(f"LOGIN_EMAIL: {login_email}")
    print(f"LOGIN_PASSWORD: {'*' * len(login_password) if login_password else 'Not set'}")
    
    if login_email and login_password:
        print("✅ Admin credentials loaded successfully from .env")
    else:
        print("❌ Admin credentials not found in environment")
        return False
    
    # Test EndUser token
    print("\n🔍 EndUser Token from .env:")
    print("-" * 40)
    
    enduser_token = os.getenv('ENDUSER_AUTH_TOKEN')
    
    if enduser_token:
        print(f"ENDUSER_AUTH_TOKEN: {enduser_token[:20]}...")
        print("✅ EndUser token loaded successfully from .env")
    else:
        print("❌ EndUser token not found in environment")
        return False
    
    # Test that classes use the environment variables
    print("\n🔍 Class Default Values vs Environment:")
    print("-" * 40)
    
    print(f"AdminUser.DEFAULT_LOGIN_EMAIL: {AdminUser.DEFAULT_LOGIN_EMAIL}")
    print(f"Environment LOGIN_EMAIL: {login_email}")
    print(f"Match: {'✅' if AdminUser.DEFAULT_LOGIN_EMAIL == login_email else '❌'}")
    
    print(f"\nAdminUser.DEFAULT_LOGIN_PASSWORD: {'*' * len(AdminUser.DEFAULT_LOGIN_PASSWORD)}")
    print(f"Environment LOGIN_PASSWORD: {'*' * len(login_password)}")
    print(f"Match: {'✅' if AdminUser.DEFAULT_LOGIN_PASSWORD == login_password else '❌'}")
    
    print(f"\nAdminUser.DEFAULT_ENDUSER_AUTH_TOKEN: {AdminUser.DEFAULT_ENDUSER_AUTH_TOKEN}")
    print(f"Environment ENDUSER_AUTH_TOKEN: {enduser_token[:20]}...")
    
    return True

def test_authentication_simulation():
    """Simulate the authentication process using .env variables."""
    
    print("\n" + "=" * 60)
    print("SIMULATING AUTHENTICATION WITH .ENV VARIABLES")
    print("=" * 60)
    
    # Simulate AdminUser login
    print("\n🔍 AdminUser Authentication Simulation:")
    print("-" * 40)
    
    admin_email = os.getenv("LOGIN_EMAIL", AdminUser.DEFAULT_LOGIN_EMAIL)
    admin_password = os.getenv("LOGIN_PASSWORD", AdminUser.DEFAULT_LOGIN_PASSWORD)
    
    print(f"Would login with email: {admin_email}")
    print(f"Would login with password: {'*' * len(admin_password)}")
    print("✅ AdminUser would use credentials from .env file")
    
    # Simulate EndUser token auth
    print("\n🔍 EndUser Authentication Simulation:")
    print("-" * 40)
    
    auth_token = os.getenv("ENDUSER_AUTH_TOKEN", EndUser.DEFAULT_ENDUSER_AUTH_TOKEN)
    
    if auth_token:
        print(f"Would use token: {auth_token[:20]}...")
        print("Would set header: Authorization: Bearer <token>")
        print("✅ EndUser would use token from .env file")
    else:
        print("Would operate in fallback mode (no authentication)")
        print("✅ EndUser would use fallback behavior")
    
    return True

def show_usage_examples():
    """Show how to use the .env file setup."""
    
    print("\n" + "=" * 60)
    print("USAGE WITH .ENV FILE")
    print("=" * 60)
    
    print("\n📁 Your .env file contains:")
    print("-" * 30)
    print('LOGIN_EMAIL="<EMAIL>"')
    print('LOGIN_PASSWORD="LvYFJfVw*q"')
    print('ENDUSER_AUTH_TOKEN="eyJhbGciOiJIUzI1NiIs..."')
    
    print("\n🚀 How to run Locust:")
    print("-" * 30)
    print("# The .env file is automatically loaded!")
    print("locust -f locustfile.py --host=https://api.credexon.com")
    print("")
    print("# Both user types will use credentials from .env:")
    print("# - AdminUser: Uses LOGIN_EMAIL and LOGIN_PASSWORD")
    print("# - EndUser: Uses ENDUSER_AUTH_TOKEN")
    
    print("\n✅ Benefits of using .env file:")
    print("-" * 30)
    print("✅ No need to set environment variables manually")
    print("✅ Credentials are automatically loaded")
    print("✅ Easy to manage and version control (add .env to .gitignore)")
    print("✅ Works across different operating systems")
    print("✅ Supports both AdminUser and EndUser authentication")

def main():
    """Run all tests."""
    
    try:
        # Test .env file loading
        if not test_env_file_loading():
            print("\n❌ .env file loading test failed!")
            return 1
        
        # Test authentication simulation
        if not test_authentication_simulation():
            print("\n❌ Authentication simulation test failed!")
            return 1
        
        # Show usage examples
        show_usage_examples()
        
        print("\n" + "=" * 60)
        print("✅ .ENV FILE INTEGRATION SUCCESSFUL!")
        print("=" * 60)
        print("Your .env file is properly loaded and both user types")
        print("will use the credentials from the .env file automatically!")
        print("\nYou can now run: locust -f locustfile.py --host=https://api.credexon.com")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
