# Example .env file for Locust load testing
# Copy this file to .env and update with your actual credentials

# Admin User Credentials (for /admin/* endpoints)
LOGIN_EMAIL="<EMAIL>"
LOGIN_PASSWORD="your-admin-password-here"

# End User Authentication Token (for /app/* endpoints)
# Get this token from your app's login flow
ENDUSER_AUTH_TOKEN="your-bearer-token-here"

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Update the values above with your actual credentials
# 3. Run Locust: locust -f locustfile.py --host=https://api.credexon.com
# 4. Add .env to your .gitignore to keep credentials secure
